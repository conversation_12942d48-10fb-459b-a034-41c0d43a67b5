package dao

import (
	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/dbutil"
	"github.com/homewizeAI/errs"
)

const (
	selUserPolicy = `SELECT user_id, policy_id, created_at FROM user_policies `
)

// scanRowToUserPolicy scans a database row into a UserPolicy struct
func scanRowToUserPolicy(row interface{ Scan(...interface{}) error }) (*model.UserPolicy, error) {
	var userPolicy model.UserPolicy
	err := row.Scan(&userPolicy.UserID, &userPolicy.PolicyID, &userPolicy.CreatedAt)
	return &userPolicy, err
}

func UserPolicyCreate(data model.UserPolicyRequest) error {
	query := `
		INSERT INTO user_policies (
			user_id, policy_id
		) VALUES (
			$1, $2
		)
	`

	_, err := dbutil.ExecGetAffRows(query, data.UserID, data.PolicyID)
	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return errs.BadInput("relationship")
		}
		if dbutil.ErrIsUniqueViolation(err) {
			return errs.BadInput("duplicate")
		}
		return err
	}

	return nil
}

func UserPolicyGetByUserID(userID uint64) ([]model.UserPolicy, error) {
	query := selUserPolicy + `WHERE user_id = $1 ORDER BY policy_id`
	rows, err := dbutil.Query(query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var userPolicies []model.UserPolicy
	for rows.Next() {
		userPolicy, err := scanRowToUserPolicy(rows)
		if err != nil {
			return nil, err
		}
		userPolicies = append(userPolicies, *userPolicy)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return userPolicies, nil
}

func UserPolicyGetByPolicyID(policyID uint64) ([]model.UserPolicy, error) {
	query := selUserPolicy + `WHERE policy_id = $1 ORDER BY user_id`
	rows, err := dbutil.Query(query, policyID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var userPolicies []model.UserPolicy
	for rows.Next() {
		userPolicy, err := scanRowToUserPolicy(rows)
		if err != nil {
			return nil, err
		}
		userPolicies = append(userPolicies, *userPolicy)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return userPolicies, nil
}

func UserPolicyDelete(userID, policyID uint64) (uint64, error) {
	query := `DELETE FROM user_policies WHERE user_id = $1 AND policy_id = $2`
	affectedRows, err := dbutil.ExecGetAffRows(query, userID, policyID)
	if err != nil {
		return 0, err
	}
	return affectedRows, nil
}

func UserPolicyList(page apitypes.Page) ([]model.UserPolicy, error) {
	query := selUserPolicy + `ORDER BY user_id, policy_id ` + page.AsSQLLimit()
	rows, err := dbutil.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var userPolicies []model.UserPolicy
	for rows.Next() {
		userPolicy, err := scanRowToUserPolicy(rows)
		if err != nil {
			return nil, err
		}
		userPolicies = append(userPolicies, *userPolicy)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return userPolicies, nil
}
