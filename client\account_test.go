package client

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
	"github.com/jarcoal/httpmock"
)

func TestAccCreate(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"POST",
		"http://localhost:3000/accmgt/account",
		func(req *http.Request) (*http.Response, error) {
			var acc model.Account
			err := json.NewDecoder(req.Body).Decode(&acc)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)

			}
			var resp apitypes.IDType
			resp.ID = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	var acc model.Account
	acc.NameShort = "Acme Inc"
	acc.NameRegistered = "Acme Incorporated"
	acc.PhoneNumber = "555-1234"
	id, err := c.AccCreate(context.Background(), acc)
	if err != nil {
		t.Error(err)
	}
	if id != 1 {
		t.Errorf("Expected id 1, got %d", id)
	}
}

func TestAccGetByID(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/account/1",
		func(req *http.Request) (*http.Response, error) {
			acc := model.Account{
				ID:             1,
				NameShort:      "Acme Inc",
				NameRegistered: "Acme Incorporated",
				PhoneNumber:    "555-1234",
			}
			return httpmock.NewJsonResponse(http.StatusOK, acc)
		},
	)

	acc, err := c.AccGetByID(context.Background(), 1)
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if acc == nil {
		t.Error("Expected account response, got nil")
	}
}

func TestAccGetByShortName(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/account/short/Acme%20Inc",
		func(req *http.Request) (*http.Response, error) {
			acc := model.Account{
				ID:             1,
				NameShort:      "Acme Inc",
				NameRegistered: "Acme Incorporated",
				PhoneNumber:    "555-1234",
			}
			return httpmock.NewJsonResponse(http.StatusOK, acc)
		},
	)

	acc, err := c.AccGetByShortName(context.Background(), "Acme Inc")
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if acc == nil {
		t.Error("Expected account response, got nil")
	}
}

func TestAccUpdate(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"PUT",
		"http://localhost:3000/accmgt/account/1",
		func(req *http.Request) (*http.Response, error) {
			var acc model.Account
			err := json.NewDecoder(req.Body).Decode(&acc)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			var resp apitypes.AffectedRowsType
			resp.AffectedRows = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	var acc model.Account
	acc.NameShort = "Acme Corp"
	acc.NameRegistered = "Acme Corporation"
	acc.PhoneNumber = "555-5678"
	rowsAffected, err := c.AccUpdate(context.Background(), 1, acc)
	if err != nil {
		t.Error(err)
	}
	if rowsAffected != 1 {
		t.Errorf("Expected 1 row affected, got %d", rowsAffected)
	}
}

func TestAccDelete(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"DELETE",
		"http://localhost:3000/accmgt/account/1",
		func(req *http.Request) (*http.Response, error) {
			var resp apitypes.AffectedRowsType
			resp.AffectedRows = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	rowsAffected, err := c.AccDelete(context.Background(), 1)
	if err != nil {
		t.Error(err)
	}
	if rowsAffected != 1 {
		t.Errorf("Expected 1 row affected, got %d", rowsAffected)
	}
}

func TestAccList(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/accounts",
		func(req *http.Request) (*http.Response, error) {
			accounts := []model.Account{
				{ID: 1, NameShort: "Acme Inc", NameRegistered: "Acme Incorporated", PhoneNumber: "555-1234"},
				{ID: 2, NameShort: "Beta Corp", NameRegistered: "Beta Corporation", PhoneNumber: "555-5678"},
			}
			return httpmock.NewJsonResponse(http.StatusOK, accounts)
		},
	)

	page := apitypes.Page{Page: 0, Size: 20}
	accounts, err := c.AccList(context.Background(), page)
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if accounts == nil {
		t.Error("Expected accounts response, got nil")
	}
}

func TestAccCount(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/accounts/count",
		func(req *http.Request) (*http.Response, error) {
			count := apitypes.CountType{Count: 42}
			return httpmock.NewJsonResponse(http.StatusOK, count)
		},
	)

	count, err := c.AccCount(context.Background())
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response (count is always >= 0 for uint64)
	_ = count // Just verify we got a response without error
}
