package api

import (
	"github.com/gin-gonic/gin"
	"github.com/homewizeAI/accmgtms/dao"
	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/errs"
	"github.com/homewizeAI/utils"
)

func RolePolicyCreate(c *gin.Context) {
	var req model.RolePolicyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		apicommon.RespBadRequest(c, "request")
		return
	}

	err := dao.RolePolicyCreate(req)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		if errs.IsConflict(err) {
			apicommon.RespConflict(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, gin.H{"message": "Role policy link created successfully"})
}

func RolePolicyGetByRoleID(c *gin.Context) {
	roleIDStr := c.Param("role_id")
	roleID, err := utils.AsUint64(roleIDStr)
	if err != nil {
		apicommon.RespBadRequest(c, "role_id")
		return
	}

	rolePolicies, err := dao.RolePolicyGetByRoleID(roleID)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	// Ensure we return an empty array instead of null
	if rolePolicies == nil {
		rolePolicies = []model.RolePolicy{}
	}

	apicommon.RespOk(c, rolePolicies)
}

func RolePolicyGetByPolicyID(c *gin.Context) {
	policyIDStr := c.Param("policy_id")
	policyID, err := utils.AsUint64(policyIDStr)
	if err != nil {
		apicommon.RespBadRequest(c, "policy_id")
		return
	}

	rolePolicies, err := dao.RolePolicyGetByPolicyID(policyID)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	// Ensure we return an empty array instead of null
	if rolePolicies == nil {
		rolePolicies = []model.RolePolicy{}
	}

	apicommon.RespOk(c, rolePolicies)
}

func RolePolicyDelete(c *gin.Context) {
	roleIDStr := c.Param("role_id")
	policyIDStr := c.Param("policy_id")

	roleID, err := utils.AsUint64(roleIDStr)
	if err != nil {
		apicommon.RespBadRequest(c, "role_id")
		return
	}

	policyID, err := utils.AsUint64(policyIDStr)
	if err != nil {
		apicommon.RespBadRequest(c, "policy_id")
		return
	}

	affectedRows, err := dao.RolePolicyDelete(roleID, policyID)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(affectedRows))
}

func RolePolicyList(c *gin.Context) {
	page := apicommon.GetPage(c)
	rolePolicies, err := dao.RolePolicyList(page)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	// Ensure we return an empty array instead of null
	if rolePolicies == nil {
		rolePolicies = []model.RolePolicy{}
	}

	apicommon.RespOk(c, rolePolicies)
}
