package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/apitypes"
)

func (c *Client) PolicyCreate(ctx context.Context, data apitypes.IDNameType) (uint64, error) {
	url := c.UrlPfx + accmgtPolicy
	return c.Client.Client.Create(ctx, url, data, nil)
}

func (c *Client) PolicyGetByID(ctx context.Context, id uint64) (*apitypes.IDNameType, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtPolicy, id)
	var result apitypes.IDNameType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) PolicyGetByName(ctx context.Context, name string) (*apitypes.IDNameType, error) {
	url := fmt.Sprintf("%s%s/name/%s", c.UrlPfx, accmgtPolicy, name)
	var result apitypes.IDNameType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) PolicyUpdate(ctx context.Context, id uint64, data apitypes.IDNameType) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtPolicy, id)
	return c.Client.Client.Update(ctx, url, data, nil)
}

func (c *Client) PolicyDelete(ctx context.Context, id uint64) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtPolicy, id)
	return c.Client.Client.Delete(ctx, url, nil)
}

func (c *Client) PolicyList(ctx context.Context, page apitypes.Page) ([]apitypes.IDNameType, error) {
	url := c.UrlPfx + accmgtPolicies + "?" + page.AsURLParams()
	var result []apitypes.IDNameType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
