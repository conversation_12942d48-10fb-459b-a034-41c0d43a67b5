package tests

import (
	"testing"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
)

func TestUserPolicyCreate(t *testing.T) {
	e := getExpect(t)

	// Create test account, role, user state, user and policy first
	account := model.Account{
		NameShort:      "TestAcc",
		NameRegistered: "Test Account",
		PhoneNumber:    "555-1234",
	}
	accResp := e.POST("/account").
		WithJSON(account).
		Expect().
		Status(200).
		JSON().Object()
	accID := uint64(accResp.Value("id").Number().Raw())
	testData.AccountIDs = append(testData.AccountIDs, accID)

	role := model.Role{Name: "TestRole"}
	roleResp := e.POST("/role").
		WithJSON(role).
		Expect().
		Status(200).
		JSON().Object()
	roleID := uint64(roleResp.Value("id").Number().Raw())
	testData.RoleIDs = append(testData.RoleIDs, roleID)

	userState := apitypes.IDNameType{Name: "TestUserState"}
	userStateResp := e.POST("/user-state").
		WithJSON(userState).
		Expect().
		Status(200).
		JSON().Object()
	userStateID := uint64(userStateResp.Value("id").Number().Raw())
	testData.UserStateIDs = append(testData.UserStateIDs, userStateID)

	user := model.User{
		AccID:       accID,
		RoleID:      roleID,
		UserStateID: userStateID,
		NameFirst:   "John",
		NameLast:    "Doe",
		Email:       "<EMAIL>",
	}
	userResp := e.POST("/user").
		WithJSON(user).
		Expect().
		Status(200).
		JSON().Object()
	userID := uint64(userResp.Value("id").Number().Raw())
	testData.UserIDs = append(testData.UserIDs, userID)

	policy := apitypes.IDNameType{Name: "TestPolicy"}
	policyResp := e.POST("/policy").
		WithJSON(policy).
		Expect().
		Status(200).
		JSON().Object()
	policyID := uint64(policyResp.Value("id").Number().Raw())
	testData.PolicyIDs = append(testData.PolicyIDs, policyID)

	// Create user-policy link
	userPolicy := model.UserPolicyRequest{
		UserID:   userID,
		PolicyID: policyID,
	}

	e.POST("/user-policy").
		WithJSON(userPolicy).
		Expect().
		Status(200).
		JSON().Object().
		ContainsKey("message")
}

func TestUserPolicyCreateValidation(t *testing.T) {
	e := getExpect(t)

	// Test missing user_id
	userPolicy := model.UserPolicyRequest{
		PolicyID: 1,
	}
	e.POST("/user-policy").
		WithJSON(userPolicy).
		Expect().
		Status(400)

	// Test missing policy_id
	userPolicy = model.UserPolicyRequest{
		UserID: 1,
	}
	e.POST("/user-policy").
		WithJSON(userPolicy).
		Expect().
		Status(400)

	// Test invalid foreign key
	userPolicy = model.UserPolicyRequest{
		UserID:   99999,
		PolicyID: 99999,
	}
	e.POST("/user-policy").
		WithJSON(userPolicy).
		Expect().
		Status(400)
}

func TestUserPolicyGetByUserID(t *testing.T) {
	e := getExpect(t)

	// Create test account, role, user state, user and policy
	account := model.Account{
		NameShort:      "TestAcc2",
		NameRegistered: "Test Account 2",
		PhoneNumber:    "555-5678",
	}
	accResp := e.POST("/account").
		WithJSON(account).
		Expect().
		Status(200).
		JSON().Object()
	accID := uint64(accResp.Value("id").Number().Raw())
	testData.AccountIDs = append(testData.AccountIDs, accID)

	role := model.Role{Name: "TestRole2"}
	roleResp := e.POST("/role").
		WithJSON(role).
		Expect().
		Status(200).
		JSON().Object()
	roleID := uint64(roleResp.Value("id").Number().Raw())
	testData.RoleIDs = append(testData.RoleIDs, roleID)

	userState := apitypes.IDNameType{Name: "TestUserState2"}
	userStateResp := e.POST("/user-state").
		WithJSON(userState).
		Expect().
		Status(200).
		JSON().Object()
	userStateID := uint64(userStateResp.Value("id").Number().Raw())
	testData.UserStateIDs = append(testData.UserStateIDs, userStateID)

	user := model.User{
		AccID:       accID,
		RoleID:      roleID,
		UserStateID: userStateID,
		NameFirst:   "Jane",
		NameLast:    "Smith",
		Email:       "<EMAIL>",
	}
	userResp := e.POST("/user").
		WithJSON(user).
		Expect().
		Status(200).
		JSON().Object()
	userID := uint64(userResp.Value("id").Number().Raw())
	testData.UserIDs = append(testData.UserIDs, userID)

	policy := apitypes.IDNameType{Name: "TestPolicy2"}
	policyResp := e.POST("/policy").
		WithJSON(policy).
		Expect().
		Status(200).
		JSON().Object()
	policyID := uint64(policyResp.Value("id").Number().Raw())
	testData.PolicyIDs = append(testData.PolicyIDs, policyID)

	// Create user-policy link
	userPolicy := model.UserPolicyRequest{
		UserID:   userID,
		PolicyID: policyID,
	}
	e.POST("/user-policy").
		WithJSON(userPolicy).
		Expect().
		Status(200)

	// Get by user ID
	resp := e.GET("/user-policy/user/{user_id}", userID).
		Expect().
		Status(200).
		JSON().Array()

	resp.Length().Ge(1)
	resp.Element(0).Object().
		Value("user_id").Number().Equal(userID).
		Value("policy_id").Number().Equal(policyID)
}

func TestUserPolicyGetByPolicyID(t *testing.T) {
	e := getExpect(t)

	// Create test account, role, user state, user and policy
	account := model.Account{
		NameShort:      "TestAcc3",
		NameRegistered: "Test Account 3",
		PhoneNumber:    "555-9999",
	}
	accResp := e.POST("/account").
		WithJSON(account).
		Expect().
		Status(200).
		JSON().Object()
	accID := uint64(accResp.Value("id").Number().Raw())
	testData.AccountIDs = append(testData.AccountIDs, accID)

	role := model.Role{Name: "TestRole3"}
	roleResp := e.POST("/role").
		WithJSON(role).
		Expect().
		Status(200).
		JSON().Object()
	roleID := uint64(roleResp.Value("id").Number().Raw())
	testData.RoleIDs = append(testData.RoleIDs, roleID)

	userState := apitypes.IDNameType{Name: "TestUserState3"}
	userStateResp := e.POST("/user-state").
		WithJSON(userState).
		Expect().
		Status(200).
		JSON().Object()
	userStateID := uint64(userStateResp.Value("id").Number().Raw())
	testData.UserStateIDs = append(testData.UserStateIDs, userStateID)

	user := model.User{
		AccID:       accID,
		RoleID:      roleID,
		UserStateID: userStateID,
		NameFirst:   "Bob",
		NameLast:    "Johnson",
		Email:       "<EMAIL>",
	}
	userResp := e.POST("/user").
		WithJSON(user).
		Expect().
		Status(200).
		JSON().Object()
	userID := uint64(userResp.Value("id").Number().Raw())
	testData.UserIDs = append(testData.UserIDs, userID)

	policy := apitypes.IDNameType{Name: "TestPolicy3"}
	policyResp := e.POST("/policy").
		WithJSON(policy).
		Expect().
		Status(200).
		JSON().Object()
	policyID := uint64(policyResp.Value("id").Number().Raw())
	testData.PolicyIDs = append(testData.PolicyIDs, policyID)

	// Create user-policy link
	userPolicy := model.UserPolicyRequest{
		UserID:   userID,
		PolicyID: policyID,
	}
	e.POST("/user-policy").
		WithJSON(userPolicy).
		Expect().
		Status(200)

	// Get by policy ID
	resp := e.GET("/user-policy/policy/{policy_id}", policyID).
		Expect().
		Status(200).
		JSON().Array()

	resp.Length().Ge(1)
	resp.Element(0).Object().
		Value("user_id").Number().Equal(userID).
		Value("policy_id").Number().Equal(policyID)
}

func TestUserPolicyDelete(t *testing.T) {
	e := getExpect(t)

	// Create test account, role, user state, user and policy
	account := model.Account{
		NameShort:      "TestAcc4",
		NameRegistered: "Test Account 4",
		PhoneNumber:    "555-7777",
	}
	accResp := e.POST("/account").
		WithJSON(account).
		Expect().
		Status(200).
		JSON().Object()
	accID := uint64(accResp.Value("id").Number().Raw())
	testData.AccountIDs = append(testData.AccountIDs, accID)

	role := model.Role{Name: "TestRole4"}
	roleResp := e.POST("/role").
		WithJSON(role).
		Expect().
		Status(200).
		JSON().Object()
	roleID := uint64(roleResp.Value("id").Number().Raw())
	testData.RoleIDs = append(testData.RoleIDs, roleID)

	userState := apitypes.IDNameType{Name: "TestUserState4"}
	userStateResp := e.POST("/user-state").
		WithJSON(userState).
		Expect().
		Status(200).
		JSON().Object()
	userStateID := uint64(userStateResp.Value("id").Number().Raw())
	testData.UserStateIDs = append(testData.UserStateIDs, userStateID)

	user := model.User{
		AccID:       accID,
		RoleID:      roleID,
		UserStateID: userStateID,
		NameFirst:   "Alice",
		NameLast:    "Brown",
		Email:       "<EMAIL>",
	}
	userResp := e.POST("/user").
		WithJSON(user).
		Expect().
		Status(200).
		JSON().Object()
	userID := uint64(userResp.Value("id").Number().Raw())
	testData.UserIDs = append(testData.UserIDs, userID)

	policy := apitypes.IDNameType{Name: "TestPolicy4"}
	policyResp := e.POST("/policy").
		WithJSON(policy).
		Expect().
		Status(200).
		JSON().Object()
	policyID := uint64(policyResp.Value("id").Number().Raw())
	testData.PolicyIDs = append(testData.PolicyIDs, policyID)

	// Create user-policy link
	userPolicy := model.UserPolicyRequest{
		UserID:   userID,
		PolicyID: policyID,
	}
	e.POST("/user-policy").
		WithJSON(userPolicy).
		Expect().
		Status(200)

	// Delete the link
	e.DELETE("/user-policy/user/{user_id}/policy/{policy_id}", userID, policyID).
		Expect().
		Status(200).
		JSON().Object().
		Value("affected_rows").Number().Equal(1)

	// Verify deletion - should return empty array
	e.GET("/user-policy/user/{user_id}", userID).
		Expect().
		Status(200).
		JSON().Array().
		Length().Equal(0)
}

func TestUserPolicyList(t *testing.T) {
	e := getExpect(t)

	// Get list
	resp := e.GET("/user-policies").
		WithQuery("page", 0).
		WithQuery("size", 20).
		Expect().
		Status(200).
		JSON().Array()

	// Should be an array (might be empty)
	resp.Length().Ge(0)
}
