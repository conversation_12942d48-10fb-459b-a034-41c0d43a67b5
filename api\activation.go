package api

import (
	"github.com/gin-gonic/gin"
	"github.com/homewizeAI/accmgtms/dao"
	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/errs"
)

// AccountActivate activates an account and user based on activation key and token
func AccountActivate(c *gin.Context) {
	var request model.ActivationRequest

	// Bind JSON payload
	if err := c.ShouldBindJSON(&request); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	// Validate required fields
	if request.Key == 0 {
		apicommon.RespBadRequest(c, "key")
		return
	}

	if request.Token == "" {
		apicommon.RespBadRequest(c, "token")
		return
	}

	if request.Subdomain == "" {
		apicommon.RespBadRequest(c, "subdomain")
		return
	}

	// Get user activation token details
	activationToken, err := dao.UserActivationTokenGetByID(request.Key)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	// Verify token matches
	if activationToken.Token != request.Token {
		apicommon.RespBadRequest(c, "token")
		return
	}

	// Get user details to verify user exists
	user, err := dao.UserGetByID(activationToken.UserID)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	// Get account details to verify account exists
	account, err := dao.AccGetByID(user.AccID)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	// Verify subdomain matches account's short name
	if account.NameShort != request.Subdomain {
		apicommon.RespBadRequest(c, "subdomain")
		return
	}

	// Call DAO function
	affectedRows, err := dao.AccountActivate(request)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	// Return success response
	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(affectedRows))
}
