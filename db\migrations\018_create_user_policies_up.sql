-- Create user_policies link table
CREATE TABLE user_policies (
    user_id INTEGER NOT NULL,
    policy_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_user_policies_user_id
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_policies_policy_id
        FOREIGN KEY (policy_id) REFERENCES policies(id) ON DELETE CASCADE,
    
    -- Composite primary key to prevent duplicates
    PRIMARY KEY (user_id, policy_id)
);

-- Create indexes for better performance
CREATE INDEX idx_user_policies_user_id ON user_policies(user_id);
CREATE INDEX idx_user_policies_policy_id ON user_policies(policy_id);
