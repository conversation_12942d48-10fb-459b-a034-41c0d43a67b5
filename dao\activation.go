package dao

import (
	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/dbutil"
	"github.com/homewizeAI/errs"
)

// AccountActivate activates an account and user based on activation key and token
func AccountActivate(request model.ActivationRequest) (uint64, error) {
	// Get user activation token details
	activationToken, err := UserActivationTokenGetByID(request.Key)
	if err != nil {
		return 0, err
	}

	// Verify token matches
	if activationToken.Token != request.Token {
		return 0, errs.BadInput("token")
	}

	// Get user details
	user, err := UserGetByID(activationToken.UserID)
	if err != nil {
		return 0, err
	}

	// Check if user is already active
	if user.UserStateID == model.UserStateActive {
		return 0, errs.BadInput("user-already-active")
	}

	// Get account details
	account, err := AccGetByID(user.AccID)
	if err != nil {
		return 0, err
	}

	// Check if account is already active
	if account.AccStateID != nil && *account.AccStateID == model.AccStateActive {
		return 0, errs.BadInput("account-already-active")
	}

	// Begin transaction for activation
	tx, err := dbutil.BeginTransaction()
	if err != nil {
		return 0, err
	}
	defer tx.Rollback() // This will be a no-op if tx.Commit() is called

	// Activate account
	activateAccountQuery := `
		UPDATE accounts
		SET acc_state_id = $1, acc_state_reason_id = $2, updated_at = CURRENT_TIMESTAMP
		WHERE id = $3
	`

	_, err = tx.ExecGetAffRows(
		activateAccountQuery,
		model.AccStateActive,
		model.AccStateReasonActiveEmailValidated,
		user.AccID)
	if err != nil {
		return 0, err
	}

	// Activate user
	activateUserQuery := `
		UPDATE users
		SET user_state_id = $1, updated_at = CURRENT_TIMESTAMP
		WHERE id = $2
	`

	affectedRows, err := tx.ExecGetAffRows(activateUserQuery, model.UserStateActive, user.ID)
	if err != nil {
		return 0, err
	}

	// Commit transaction
	err = tx.Commit()
	if err != nil {
		return 0, err
	}

	return affectedRows, nil
}
