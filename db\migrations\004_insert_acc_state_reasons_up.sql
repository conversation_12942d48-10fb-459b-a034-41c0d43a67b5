-- Insert initial data for acc_state_reasons table
-- Reasons for Active state (acc_state_id = 10): Email Validated
-- Reasons for RO state (acc_state_id = 20): Not Activated, InitDeletion
-- Reasons for Locked state (acc_state_id = 30): Backend, PaymentDefaulted
-- Reasons for Deleted state (acc_state_id = 40): CustomerRequested, Backend

INSERT INTO acc_state_reason (id, acc_state_id, reason) VALUES
(10, 10, 'Email Validated'),
(20, 20, 'Not Activated'),
(30, 20, 'InitDeletion'),
(40, 30, 'Backend'),
(50, 30, 'PaymentDefaulted'),
(60, 40, 'CustomerRequested'),
(70, 40, 'Backend');