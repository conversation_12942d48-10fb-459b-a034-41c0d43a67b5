package client

const (
	urlBase = "/accmgt"

	// Account URLs
	accmgtAccount                  = urlBase + "/account"
	accmgtAccounts                 = urlBase + "/accounts"
	accmgtAccountsCount            = urlBase + "/accounts/count"
	accmgtAccountCheckAvailability = urlBase + "/account/check-availability"

	// Role URLs
	accmgtRole  = urlBase + "/role"
	accmgtRoles = urlBase + "/roles"

	// User URLs
	accmgtUser   = urlBase + "/user"
	accmgtUserBy = urlBase + "/user/by"
	accmgtUsers  = urlBase + "/users"

	// AccState URLs
	accmgtAccState  = urlBase + "/acc-state"
	accmgtAccStates = urlBase + "/acc-states"

	// AccBlacklist URLs
	accmgtAccBlacklist  = urlBase + "/acc-blacklist"
	accmgtAccBlacklists = urlBase + "/acc-blacklists"

	// Policy URLs
	accmgtPolicy   = urlBase + "/policy"
	accmgtPolicies = urlBase + "/policies"

	// Permission URLs
	accmgtPermission  = urlBase + "/permission"
	accmgtPermissions = urlBase + "/permissions"

	// AccStateReason URLs
	accmgtAccStateReason  = urlBase + "/acc-state-reason"
	accmgtAccStateReasons = urlBase + "/acc-state-reasons"

	// UserActivationToken URLs
	accmgtUserActivationToken = urlBase + "/user-activation-token"

	// UserState URLs
	accmgtUserState  = urlBase + "/user-state"
	accmgtUserStates = urlBase + "/user-states"

	// Signup URL
	accmgtSignup = urlBase + "/signup"
)
