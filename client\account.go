package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
)

func (c *Client) AccCreate(ctx context.Context, data model.Account) (uint64, error) {
	url := c.UrlPfx + accmgtAccount
	return c.Client.Client.Create(ctx, url, data, nil)
}

func (c *Client) AccGetByID(ctx context.Context, id uint64) (*model.Account, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtAccount, id)
	var result model.Account
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) AccGetByShortName(ctx context.Context, shortName string) (*model.Account, error) {
	url := fmt.Sprintf("%s%s/short/%s", c.UrlPfx, accmgtAccount, shortName)
	var result model.Account
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) AccUpdate(ctx context.Context, id uint64, data model.Account) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtAccount, id)
	return c.Client.Client.Update(ctx, url, data, nil)
}

func (c *Client) AccDelete(ctx context.Context, id uint64) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtAccount, id)
	return c.Client.Client.Delete(ctx, url, nil)
}

func (c *Client) AccList(ctx context.Context, page apitypes.Page) ([]model.Account, error) {
	url := c.UrlPfx + accmgtAccounts + "?" + page.AsURLParams()
	var result []model.Account
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) AccCount(ctx context.Context) (uint64, error) {
	url := c.UrlPfx + accmgtAccountsCount
	var result apitypes.CountType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return 0, err
	}
	return result.Count, nil
}

func (c *Client) AccShortNameIsAvailable(ctx context.Context, shortName string) (apitypes.BoolType, error) {
	url := fmt.Sprintf("%s%s/%s", c.UrlPfx, accmgtAccountCheckAvailability, shortName)
	var result apitypes.BoolType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return apitypes.BoolTypeNew(false), err
	}

	return result, nil
}
