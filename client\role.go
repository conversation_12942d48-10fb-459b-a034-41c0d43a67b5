package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
)

func (c *Client) RoleCreate(ctx context.Context, data model.Role) (uint64, error) {
	url := c.UrlPfx + accmgtRole
	return c.Client.Client.Create(ctx, url, data, nil)
}

func (c *Client) RoleGetByID(ctx context.Context, id uint64) (*model.Role, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtRole, id)
	var result model.Role
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) RoleGetByName(ctx context.Context, name string) (*model.Role, error) {
	url := fmt.Sprintf("%s%s/%s", c.UrlPfx, accmgt<PERSON>ole, name)
	var result model.Role
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) RoleUpdate(ctx context.Context, id uint64, data model.Role) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtRole, id)
	return c.Client.Client.Update(ctx, url, data, nil)
}

func (c *Client) RoleDelete(ctx context.Context, id uint64) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtRole, id)
	return c.Client.Client.Delete(ctx, url, nil)
}

func (c *Client) RoleList(ctx context.Context, page apitypes.Page) ([]model.Role, error) {
	url := c.UrlPfx + accmgtRoles + "?" + page.AsURLParams()
	var result []model.Role
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
