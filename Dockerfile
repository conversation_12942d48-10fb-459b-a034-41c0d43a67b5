# Build stage
FROM golang:1.23-alpine AS builder

# Set GOTOOLCHAIN to auto to allow Go to download the required version
ENV GOTOOLCHAIN=auto

# Install git (required for Go modules)
RUN apk add --no-cache git
RUN mkdir -p /root/.ssh
RUN ssh-keyscan github.com > /root/.ssh/known_hosts
ARG GIT_USER_NAME=todo
ARG GIT_USER_EMAIL=<EMAIL>
RUN git config --global user.name "$GIT_USER_NAME"
RUN git config --global user.email "$GIT_USER_EMAIL"
RUN git config --global url."**************:".insteadOf "https://github.com/"

RUN mkdir /workdir
COPY . /workdir
# Set working directory
WORKDIR /workdir

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o accmgtms

# Final stage
FROM alpine:latest

RUN addgroup -g 1001 homewize
RUN adduser -u 1001 -G homewize --disabled-password --no-create-home homewize

WORKDIR /app
RUN chown -R homewize:homewize /app

USER homewize

COPY --from=builder --chown=homewize:homewize /workdir/accmgtms .
COPY --from=builder --chown=homewize:homewize /workdir/docker/entrypoint.sh .

# Expose port
EXPOSE 3000

# Run the application
ENTRYPOINT ["./entrypoint.sh"]
