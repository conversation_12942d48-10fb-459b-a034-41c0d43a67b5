package tests

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/homewizeAI/accmgtms/model"
)

func TestActivation(t *testing.T) {
	e := getExpect(t)

	// First, create a signup to get activation key and token
	timestamp := time.Now().UnixNano()
	signupRequest := model.SignupRequest{
		Account: model.Account{
			NameRegistered: fmt.Sprintf("Activation Test Company %d", timestamp),
			NameShort:      fmt.Sprintf("activationtest%d", timestamp),
			PhoneNumber:    "555-9999",
		},
		User: model.User{
			NameFirst: "Activation",
			NameLast:  "Test",
			Email:     fmt.Sprintf("<EMAIL>", timestamp),
		},
	}

	// Create signup
	signupResp := e.POST("/accmgt/signup").
		WithJSON(signupRequest).
		Expect().
		Status(http.StatusOK).
		JSON().Object()

	activationKeyID := signupResp.Value("activation_key_id").Number().Raw()
	activationToken := signupResp.Value("activation_token").String().Raw()
	accID := signupResp.Value("acc_id").Number().Raw()
	userID := signupResp.Value("user_id").Number().Raw()

	t.Logf("Created signup - AccID: %.0f, UserID: %.0f, KeyID: %.0f", accID, userID, activationKeyID)

	// Test successful activation
	activationRequest := model.ActivationRequest{
		Key:       uint64(activationKeyID),
		Token:     activationToken,
		Subdomain: fmt.Sprintf("activationtest%d", timestamp),
	}

	activationResp := e.PATCH("/accmgt/activate").
		WithJSON(activationRequest).
		Expect().
		Status(http.StatusOK).
		JSON().Object()

	activationResp.Value("affected_rows").Number().IsEqual(1)

	t.Logf("Account activation successful!")

	// Test activation with invalid key
	e.PATCH("/accmgt/activate").
		WithJSON(model.ActivationRequest{
			Key:       99999,
			Token:     "invalid-token",
			Subdomain: "invalid",
		}).
		Expect().
		Status(http.StatusNotFound).
		JSON().Object().Value("reason").String().IsEqual("user-activation-token")

	// Test activation with invalid token
	e.PATCH("/accmgt/activate").
		WithJSON(model.ActivationRequest{
			Key:       uint64(activationKeyID),
			Token:     "wrong-token",
			Subdomain: fmt.Sprintf("activationtest%d", timestamp),
		}).
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("token")

	// Test activation with wrong subdomain
	e.PATCH("/accmgt/activate").
		WithJSON(model.ActivationRequest{
			Key:       uint64(activationKeyID),
			Token:     activationToken,
			Subdomain: "wrong-subdomain",
		}).
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("subdomain")

	// Test activation when already activated
	e.PATCH("/accmgt/activate").
		WithJSON(activationRequest).
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("user-already-active")

	// Test missing subdomain - JSON binding fails before validation
	e.PATCH("/accmgt/activate").
		WithJSON(model.ActivationRequest{
			Key:   uint64(activationKeyID),
			Token: activationToken,
		}).
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("payload")

	// Test invalid payload - JSON binding fails before validation
	e.PATCH("/accmgt/activate").
		WithJSON(map[string]interface{}{"key": 0}).
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("payload")

	e.PATCH("/accmgt/activate").
		WithJSON(map[string]interface{}{"key": 123, "token": ""}).
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("payload")

	// Verify account and user are now active by checking their states
	accountResp := e.GET(fmt.Sprintf("/accmgt/account/%.0f", accID)).
		Expect().
		Status(http.StatusOK).
		JSON().Object()

	accountResp.Value("acc_state_id").Number().IsEqual(model.AccStateActive)

	userResp := e.GET(fmt.Sprintf("/accmgt/user/%.0f", userID)).
		Expect().
		Status(http.StatusOK).
		JSON().Object()

	userResp.Value("user_state_id").Number().IsEqual(model.UserStateActive)

	t.Logf("Activation test completed successfully!")

	// Cleanup
	cleanupTestData()
}
