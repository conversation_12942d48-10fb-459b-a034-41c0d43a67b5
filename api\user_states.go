package api

import (
	"github.com/gin-gonic/gin"

	"github.com/homewizeAI/accmgtms/dao"
	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/errs"
	"github.com/homewizeAI/utils"
)

func UserStateCreate(c *gin.Context) {
	var err error
	var data apitypes.IDNameType
	if err = c.BindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	id, err := dao.UserStateCreate(data)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		if errs.IsConflict(err) {
			apicommon.RespConflict(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}
	apicommon.RespOk(c, apitypes.IDTypeNew(id))
}

func UserStateGetByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	userState, err := dao.UserStateGetByID(id)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, userState)
}

func UserStateGetByName(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		apicommon.RespBadRequest(c, "name")
		return
	}

	userState, err := dao.UserStateGetByName(name)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, userState)
}

func UserStateUpdate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	var data apitypes.IDNameType
	if err = c.BindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	rowsAffected, err := dao.UserStateUpdate(id, data)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(rowsAffected))
}

func UserStateDelete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	err = dao.UserStateDelete(id)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.Resp204(c)
}

func UserStateList(c *gin.Context) {
	page := apicommon.GetPage(c)
	userStates, err := dao.UserStateList(page)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, userStates)
}
