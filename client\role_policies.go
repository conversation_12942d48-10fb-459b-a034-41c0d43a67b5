package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
)

func (c *Client) RolePolicyCreate(ctx context.Context, data model.RolePolicyRequest) error {
	url := c.UrlPfx + accmgtRolePolicy
	_, err := c.Client.Client.Create(ctx, url, data, nil)
	return err
}

func (c *Client) RolePolicyGetByRoleID(ctx context.Context, roleID uint64) ([]model.RolePolicy, error) {
	url := fmt.Sprintf("%s%s/role/%d", c.UrlPfx, accmgtRolePolicy, roleID)
	var result []model.RolePolicy
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) RolePolicyGetByPolicyID(ctx context.Context, policyID uint64) ([]model.RolePolicy, error) {
	url := fmt.Sprintf("%s%s/policy/%d", c.UrlPfx, accmgtRolePolicy, policyID)
	var result []model.RolePolicy
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) RolePolicyDelete(ctx context.Context, roleID, policyID uint64) (uint64, error) {
	url := fmt.Sprintf("%s%s/role/%d/policy/%d", c.UrlPfx, accmgtRolePolicy, roleID, policyID)
	return c.Client.Client.Delete(ctx, url, nil)
}

func (c *Client) RolePolicyList(ctx context.Context, page apitypes.Page) ([]model.RolePolicy, error) {
	url := c.UrlPfx + accmgtRolePolicies + "?" + page.AsURLParams()
	var result []model.RolePolicy
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
