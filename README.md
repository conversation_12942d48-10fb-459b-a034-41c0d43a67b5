# Account Management Service (accmgtms)

A REST API service for managing accounts built with Go, Gin framework, and PostgreSQL.

## Features

- **Complete CRUD Operations**: Create, Read, Update, Delete accounts
- **Pagination Support**: List accounts with page/size parameters
- **Input Validation**: Comprehensive validation and error handling
- **Database Integration**: PostgreSQL with proper indexing
- **Health Checks**: Database connectivity monitoring
- **Standardized Responses**: Consistent API response formats
- **Comprehensive Testing**: Full test suite with positive/negative cases

## API Endpoints

### Account Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/accounts` | Create a new account |
| `GET` | `/accounts/:id` | Get account by ID |
| `GET` | `/accounts/short/:shortName` | Get account by short name |
| `PUT` | `/accounts/:id` | Update an existing account |
| `DELETE` | `/accounts/:id` | Delete an account |
| `GET` | `/accounts?page=0&size=20` | List accounts with pagination |
| `GET` | `/accounts/count` | Get total count of accounts |

### Utility Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/health` | Health check endpoint |