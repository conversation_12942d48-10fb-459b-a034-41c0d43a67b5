package dao

import (
	"database/sql"

	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/dbutil"
	"github.com/homewizeAI/errs"
)

const (
	selAccState = `SELECT id, name FROM acc_states `
)

// scanRowToAccState scans a database row into an IDNameType struct
func scanRowToAccState(scanner dbutil.Scanner) (*apitypes.IDNameType, error) {
	var accState apitypes.IDNameType
	err := scanner.Scan(
		&accState.ID,
		&accState.Name,
	)
	if err != nil {
		return nil, err
	}
	return &accState, nil
}

func AccStateCreate(data apitypes.IDNameType) (uint64, error) {
	query := `
		INSERT INTO acc_states (
			name
		) VALUES (
			$1
		)
	`

	id, err := dbutil.ExecGetID(
		query,
		data.Name,
	)

	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, errs.BadInput("relationship")
		}
		if dbutil.ErrIsUniqueViolation(err) {
			return 0, errs.BadInput("name")
		}
		return 0, err
	}

	return id, nil
}

// AccStateGetByID retrieves an acc_state by its ID
func AccStateGetByID(id uint64) (*apitypes.IDNameType, error) {
	query := selAccState + ` WHERE id = $1 `

	row := dbutil.QueryRow(query, id)

	accState, err := scanRowToAccState(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("acc-state")
		}
		return nil, err
	}

	return accState, nil
}

// AccStateGetByName retrieves an acc_state by its name
func AccStateGetByName(name string) (*apitypes.IDNameType, error) {
	query := selAccState + ` WHERE name = $1 `

	row := dbutil.QueryRow(query, name)

	accState, err := scanRowToAccState(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("acc-state")
		}
		return nil, err
	}

	return accState, nil
}

// AccStateUpdate updates an existing acc_state
func AccStateUpdate(id uint64, data apitypes.IDNameType) (uint64, error) {
	query := `
		UPDATE acc_states SET
			name = $1,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = $2
	`

	rowsAffected, err := dbutil.ExecGetAffRows(
		query,
		data.Name,
		id,
	)

	if err != nil {
		if dbutil.ErrIsUniqueViolation(err) {
			return 0, errs.BadInput("name")
		}
		return 0, err
	}

	if rowsAffected == 0 {
		return 0, errs.NotFound("acc-state")
	}

	return rowsAffected, nil
}

// AccStateDelete deletes an acc_state by ID
func AccStateDelete(id uint64) error {
	query := `DELETE FROM acc_states WHERE id = $1`

	rowsAffected, err := dbutil.ExecGetAffRows(query, id)
	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return errs.BadInput("acc-state-in-use")
		}
		return err
	}

	if rowsAffected == 0 {
		return errs.NotFound("acc-state")
	}

	return nil
}

// AccStateList retrieves a list of acc_states with pagination
func AccStateList(page apitypes.Page) ([]*apitypes.IDNameType, error) {
	query := selAccState + ` ORDER BY name ASC ` + page.AsSQLLimit()

	rows, err := dbutil.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var accStates []*apitypes.IDNameType
	for rows.Next() {
		accState, err := scanRowToAccState(rows)
		if err != nil {
			return nil, err
		}
		accStates = append(accStates, accState)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return accStates, nil
}
