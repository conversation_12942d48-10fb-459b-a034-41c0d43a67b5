package api

import (
	"github.com/gin-gonic/gin"

	"github.com/homewizeAI/accmgtms/dao"
	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/errs"
	"github.com/homewizeAI/utils"
)

func UserCreate(c *gin.Context) {
	var err error
	var data model.User
	if err = c.BindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	id, err := dao.UserCreate(data)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		if errs.IsConflict(err) {
			apicommon.RespConflict(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}
	apicommon.RespOk(c, apitypes.IDTypeNew(id))
}

func UserGetByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	user, err := dao.UserGetByID(id)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, user)
}

func UserGetByEmail(c *gin.Context) {
	email := c.Param("email")
	if email == "" {
		apicommon.RespBadRequest(c, "email")
		return
	}

	user, err := dao.UserGetByEmail(email)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, user)
}

func UserUpdate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	var data model.User
	if err = c.BindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	rowsAffected, err := dao.UserUpdate(id, data)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(rowsAffected))
}

func UserDelete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	rowsAffected, err := dao.UserDelete(id)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(rowsAffected))
}

func UserList(c *gin.Context) {
	page := apicommon.GetPage(c)
	users, err := dao.UserList(page)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, users)
}

// UsrBy retrieves a user based on different query parameter combinations
// Supports three combinations:
// 1. key & token (activation key and token)
// 2. email & shortName (user email and account short name)
// 3. shortName only (account short name)
func UsrBy(c *gin.Context) {
	// Get query parameters
	key := c.Query("key")
	token := c.Query("token")
	email := c.Query("email")
	shortName := c.Query("shortName")

	var user *model.User
	var err error

	// Handle combination 1: key & token
	if key != "" && token != "" {
		keyUint64, parseErr := utils.AsUint64(key)
		if parseErr != nil {
			apicommon.RespBadRequest(c, "key")
			return
		}
		user, err = dao.UserGetByKeyAndToken(keyUint64, token)
	} else if email != "" && shortName != "" {
		// Handle combination 2: email & shortName
		user, err = dao.UserGetByEmailAndShortName(email, shortName)
	} else if shortName != "" {
		// Handle combination 3: shortName only
		user, err = dao.UserGetByAccShortName(shortName)
	}

	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, user)
}

func UserIsActive(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	isActive, err := dao.UserIsActive(id)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
	}
	apicommon.RespOk(c, apitypes.BoolTypeNew(isActive))
}
