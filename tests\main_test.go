package tests

import (
	"fmt"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/gavv/httpexpect/v2"
	"github.com/gin-gonic/gin"
	_ "github.com/lib/pq"

	"github.com/homewizeAI/accmgtms/api"
	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apicommongin"
	"github.com/homewizeAI/dbutil"
)

var (
	server *httptest.Server
)

// TestMain sets up the test environment
func TestMain(m *testing.M) {
	// Change to parent directory to find conf/secrets.toml
	if err := os.Chdir(".."); err != nil {
		panic(fmt.Sprintf("Failed to change directory: %v", err))
	}

	// Set environment variable for database password
	os.Setenv("PGPASSWORD", "postgres")

	// Initialize database using dbutil
	dbutil.Init()
	defer dbutil.Close()

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create test router with same routes as main.go
	router := setupTestRouter()

	// Create test server
	server = httptest.NewServer(router)
	defer server.Close()

	// Run tests
	code := m.Run()

	// Clean up any remaining test data
	cleanupTestData()

	os.Exit(code)
}

// getExpect returns an httpexpect instance for the given test
func getExpect(t *testing.T) *httpexpect.Expect {
	return httpexpect.Default(t, server.URL)
}

// setupTestRouter creates the same router configuration as main.go
func setupTestRouter() *gin.Engine {
	r := apicommongin.NewRouter()

	// Account routes
	g := r.Group("/accmgt")

	g.POST("/account", api.AccCreate)
	g.GET("/account/:id", api.AccGetByID)
	g.GET("/account/short/:shortName", api.AccGetByShortName)
	g.PUT("/account/:id", api.AccUpdate)
	g.DELETE("/account/:id", api.AccDelete)
	g.GET("/accounts", api.AccList)

	g.POST("/role", api.RoleCreate)
	g.GET("/role/:id", api.RoleGetByID)
	g.GET("/role/name/:name", api.RoleGetByName)
	g.PUT("/role/:id", api.RoleUpdate)
	g.DELETE("/role/:id", api.RoleDelete)
	g.GET("/roles", api.RoleList)

	g.POST("/user", api.UserCreate)
	g.GET("/user/:id", api.UserGetByID)
	g.GET("/user/email/:email", api.UserGetByEmail)
	g.GET("/user/by", api.UsrBy)
	g.PUT("/user/:id", api.UserUpdate)
	g.DELETE("/user/:id", api.UserDelete)
	g.GET("/users", api.UserList)

	g.POST("/acc-state", api.AccStateCreate)
	g.GET("/acc-state/:id", api.AccStateGetByID)
	g.GET("/acc-state/name/:name", api.AccStateGetByName)
	g.PUT("/acc-state/:id", api.AccStateUpdate)
	g.DELETE("/acc-state/:id", api.AccStateDelete)
	g.GET("/acc-states", api.AccStateList)

	g.POST("/acc-blacklist", api.AccBlacklistCreate)
	g.GET("/acc-blacklist/:id", api.AccBlacklistGetByID)
	g.GET("/acc-blacklist/name/:name", api.AccBlacklistGetByName)
	g.PUT("/acc-blacklist/:id", api.AccBlacklistUpdate)
	g.DELETE("/acc-blacklist/:id", api.AccBlacklistDelete)
	g.GET("/acc-blacklists", api.AccBlacklistList)

	g.POST("/policy", api.PolicyCreate)
	g.GET("/policy/:id", api.PolicyGetByID)
	g.GET("/policy/name/:name", api.PolicyGetByName)
	g.PUT("/policy/:id", api.PolicyUpdate)
	g.DELETE("/policy/:id", api.PolicyDelete)
	g.GET("/policies", api.PolicyList)

	g.POST("/permission", api.PermissionCreate)
	g.GET("/permission/:id", api.PermissionGetByID)
	g.GET("/permission/name/:name", api.PermissionGetByName)
	g.PUT("/permission/:id", api.PermissionUpdate)
	g.DELETE("/permission/:id", api.PermissionDelete)
	g.GET("/permissions", api.PermissionList)

	// Policy-Permission link routes
	g.POST("/policy-perm", api.PolicyPermCreate)
	g.GET("/policy-perm/policy/:policy_id", api.PolicyPermGetByPolicyID)
	g.GET("/policy-perm/permission/:permission_id", api.PolicyPermGetByPermissionID)
	g.DELETE("/policy-perm/policy/:policy_id/permission/:permission_id", api.PolicyPermDelete)
	g.GET("/policy-perms", api.PolicyPermList)

	// Role-Policy link routes
	g.POST("/role-policy", api.RolePolicyCreate)
	g.GET("/role-policy/role/:role_id", api.RolePolicyGetByRoleID)
	g.GET("/role-policy/policy/:policy_id", api.RolePolicyGetByPolicyID)
	g.DELETE("/role-policy/role/:role_id/policy/:policy_id", api.RolePolicyDelete)
	g.GET("/role-policies", api.RolePolicyList)

	// User-Policy link routes
	g.POST("/user-policy", api.UserPolicyCreate)
	g.GET("/user-policy/user/:user_id", api.UserPolicyGetByUserID)
	g.GET("/user-policy/policy/:policy_id", api.UserPolicyGetByPolicyID)
	g.DELETE("/user-policy/user/:user_id/policy/:policy_id", api.UserPolicyDelete)
	g.GET("/user-policies", api.UserPolicyList)

	g.POST("/acc-state-reason", api.AccStateReasonCreate)
	g.GET("/acc-state-reason/:id", api.AccStateReasonGetByID)
	g.PUT("/acc-state-reason/:id", api.AccStateReasonUpdate)
	g.DELETE("/acc-state-reason/:id", api.AccStateReasonDelete)
	g.GET("/acc-state-reasons", api.AccStateReasonList)

	g.POST("/user-activation-token/:userID", api.UserActivationTokenCreate)
	g.GET("/user-activation-token/:id", api.UserActivationTokenGetByID)
	g.GET("/user-activation-token/token/:token", api.UserActivationTokenGetByToken)
	g.GET("/user-activation-token/user/:userID", api.UserActivationTokenGetByUserID)
	g.PUT("/user-activation-token/:id", api.UserActivationTokenUpdate)
	g.DELETE("/user-activation-token/:id", api.UserActivationTokenDelete)

	g.POST("/user-state", api.UserStateCreate)
	g.GET("/user-state/:id", api.UserStateGetByID)
	g.GET("/user-state/name/:name", api.UserStateGetByName)
	g.PUT("/user-state/:id", api.UserStateUpdate)
	g.DELETE("/user-state/:id", api.UserStateDelete)
	g.GET("/user-states", api.UserStateList)

	g.POST("/signup", api.Signup)

	// Activation route
	g.PATCH("/activate", api.AccountActivate)

	// Additional utility routes
	g.GET("/accounts/count", api.AccCount)
	g.GET("/account/check-availability/:shortName", api.AccShortNameIsAvailable)

	return r
}

// Test data cleanup utilities
type TestData struct {
	AccountIDs             []uint64
	UserIDs                []uint64
	RoleIDs                []uint64
	AccStateIDs            []uint64
	AccStateReasonIDs      []uint64
	AccBlacklistIDs        []uint64
	PolicyIDs              []uint64
	PermissionIDs          []uint64
	UserStateIDs           []uint64
	UserActivationTokenIDs []uint64
}

var testData = &TestData{}

// Helper function to clean up test data
func cleanupTestData() {
	db := dbutil.GetDB()

	// Clean up in reverse order of dependencies
	for _, id := range testData.UserActivationTokenIDs {
		db.Exec("DELETE FROM user_activation_tokens WHERE id = $1", id)
	}

	for _, id := range testData.UserIDs {
		db.Exec("DELETE FROM users WHERE id = $1", id)
	}

	for _, id := range testData.AccountIDs {
		db.Exec("DELETE FROM accounts WHERE id = $1", id)
	}

	for _, id := range testData.RoleIDs {
		db.Exec("DELETE FROM roles WHERE id = $1", id)
	}

	for _, id := range testData.AccStateReasonIDs {
		db.Exec("DELETE FROM acc_state_reasons WHERE id = $1", id)
	}

	for _, id := range testData.AccStateIDs {
		db.Exec("DELETE FROM acc_states WHERE id = $1", id)
	}

	for _, id := range testData.AccBlacklistIDs {
		db.Exec("DELETE FROM acc_blacklists WHERE id = $1", id)
	}

	for _, id := range testData.PolicyIDs {
		db.Exec("DELETE FROM policies WHERE id = $1", id)
	}

	for _, id := range testData.PermissionIDs {
		db.Exec("DELETE FROM permissions WHERE id = $1", id)
	}

	for _, id := range testData.UserStateIDs {
		db.Exec("DELETE FROM user_states WHERE id = $1", id)
	}

	// Reset test data
	testData = &TestData{}
}

// Helper function to create test account
func createTestAccount() model.Account {
	timestamp := time.Now().UnixNano()
	return model.Account{
		NameRegistered: fmt.Sprintf("Test Company %d", timestamp),
		NameShort:      fmt.Sprintf("testco%d", timestamp),
		PhoneNumber:    "555-1234",
	}
}

// Helper function to create test user
func createTestUser(accID uint64) model.User {
	timestamp := time.Now().UnixNano()
	return model.User{
		AccID:       accID,
		RoleID:      model.RoleAdmin, // Use existing role
		UserStateID: 10,              // Use existing user state
		NameFirst:   "John",
		NameLast:    "Doe",
		Email:       fmt.Sprintf("<EMAIL>", timestamp),
	}
}

// Helper function to create test role
func createTestRole() model.Role {
	timestamp := time.Now().UnixNano()
	return model.Role{
		Name: fmt.Sprintf("TestRole%d", timestamp),
	}
}
