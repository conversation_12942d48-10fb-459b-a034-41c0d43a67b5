package dao

import (
	"database/sql"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/dbutil"
	"github.com/homewizeAI/errs"
)

const (
	selRole = `select id, name, created_at, updated_at from roles `
)

// scanRowToRole scans a database row into a Role struct
func scanRowToRole(scanner dbutil.Scanner) (*model.Role, error) {
	var role model.Role
	err := scanner.Scan(
		&role.ID,
		&role.Name,
		&role.CreatedAt,
		&role.UpdatedAt,
	)
	if err != nil {
		return nil, err
	}
	return &role, nil
}

func RoleCreate(data model.Role) (uint64, error) {
	query := `
		INSERT INTO roles (
			name
		) VALUES (
			$1
		) RETURNING id
	`

	var id uint64
	err := dbutil.QueryRow(query, data.Name).Scan(&id)

	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, errs.BadInput("relationship")
		}
		if dbutil.ErrIsUniqueViolation(err) {
			return 0, errs.BadInput("name")
		}
		return 0, err
	}

	return id, nil
}

// RoleGetByID retrieves a role by its ID
func RoleGetByID(id uint64) (*model.Role, error) {
	query := selRole + ` WHERE id = $1 `

	row := dbutil.QueryRow(query, id)

	role, err := scanRowToRole(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("role")
		}
		return nil, err
	}

	return role, nil
}

// RoleGetByName retrieves a role by its name
func RoleGetByName(name string) (*model.Role, error) {
	query := selRole + ` WHERE name = $1 `

	row := dbutil.QueryRow(query, name)

	role, err := scanRowToRole(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("role")
		}
		return nil, err
	}

	return role, nil
}

// RoleUpdate updates an existing role
func RoleUpdate(id uint64, data model.Role) (uint64, error) {
	query := `
		UPDATE roles SET
			name = $1,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = $2
	`

	rowsAffected, err := dbutil.ExecGetAffRows(
		query,
		data.Name,
		id,
	)

	if err != nil {
		if dbutil.ErrIsUniqueViolation(err) {
			return 0, errs.BadInput("name")
		}
		return 0, err
	}

	return rowsAffected, nil
}

// RoleDelete deletes a role by ID
func RoleDelete(id uint64) (uint64, error) {
	query := `DELETE FROM roles WHERE id = $1`

	rowsAffected, err := dbutil.ExecGetAffRows(query, id)
	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, errs.BadInput("role-in-use")
		}
		return 0, err
	}

	return rowsAffected, nil
}

// RoleList retrieves a list of roles with pagination
func RoleList(page apitypes.Page) ([]*model.Role, error) {
	query := selRole + ` ORDER BY created_at DESC ` + page.AsSQLLimit()

	rows, err := dbutil.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var roles []*model.Role
	for rows.Next() {
		role, err := scanRowToRole(rows)
		if err != nil {
			return nil, err
		}
		roles = append(roles, role)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return roles, nil
}
