package tests

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/homewizeAI/apitypes"
)

func TestAccBlacklistOperations(t *testing.T) {
	defer cleanupTestData()
	e := getExpect(t)

	// Test AccBlacklist creation
	timestamp := time.Now().Unix()
	accBlacklist := apitypes.IDNameType{
		Name: fmt.Sprintf("TestBlacklist%d", timestamp),
	}

	// Test successful creation
	createResp := e.POST("/accmgt/acc-blacklist").
		WithJSON(accBlacklist).
		Expect().
		Status(http.StatusOK).
		JSON()

	createResp.Object().ContainsKey("id")
	accBlacklistID := uint64(createResp.Object().Value("id").Number().Raw())
	testData.AccBlacklistIDs = append(testData.AccBlacklistIDs, accBlacklistID)

	t.Logf("Created AccBlacklist with ID: %d", accBlacklistID)

	// Test get by ID
	getResp := e.GET(fmt.Sprintf("/accmgt/acc-blacklist/%d", accBlacklistID)).
		Expect().
		Status(http.StatusOK).
		JSON()

	getResp.Object().Value("id").Number().IsEqual(float64(accBlacklistID))
	getResp.Object().Value("name").String().IsEqual(accBlacklist.Name)

	// Test get by name
	getByNameResp := e.GET(fmt.Sprintf("/accmgt/acc-blacklist/name/%s", accBlacklist.Name)).
		Expect().
		Status(http.StatusOK).
		JSON()

	getByNameResp.Object().Value("id").Number().IsEqual(float64(accBlacklistID))
	getByNameResp.Object().Value("name").String().IsEqual(accBlacklist.Name)

	// Test update
	updatedAccBlacklist := apitypes.IDNameType{
		Name: fmt.Sprintf("UpdatedBlacklist%d", timestamp),
	}

	updateResp := e.PUT(fmt.Sprintf("/accmgt/acc-blacklist/%d", accBlacklistID)).
		WithJSON(updatedAccBlacklist).
		Expect().
		Status(http.StatusOK).
		JSON()

	updateResp.Object().Value("affected_rows").Number().IsEqual(1)

	// Verify update
	getUpdatedResp := e.GET(fmt.Sprintf("/accmgt/acc-blacklist/%d", accBlacklistID)).
		Expect().
		Status(http.StatusOK).
		JSON()

	getUpdatedResp.Object().Value("name").String().IsEqual(updatedAccBlacklist.Name)

	// Test list
	listResp := e.GET("/accmgt/acc-blacklists").
		WithQuery("page", 0).
		WithQuery("size", 20).
		Expect().
		Status(http.StatusOK).
		JSON()

	listResp.Array().Length().Ge(1)

	// Test delete
	deleteResp := e.DELETE(fmt.Sprintf("/accmgt/acc-blacklist/%d", accBlacklistID)).
		Expect().
		Status(http.StatusOK).
		JSON()

	deleteResp.Object().Value("affected_rows").Number().IsEqual(1)

	// Verify deletion
	e.GET(fmt.Sprintf("/accmgt/acc-blacklist/%d", accBlacklistID)).
		Expect().
		Status(http.StatusNotFound)

	t.Log("AccBlacklist operations test passed!")
}

func TestAccBlacklistValidation(t *testing.T) {
	defer cleanupTestData()
	e := getExpect(t)

	// Test creation with empty name
	e.POST("/accmgt/acc-blacklist").
		WithJSON(apitypes.IDNameType{Name: ""}).
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("name")

	// Test creation with invalid payload (empty name after JSON binding)
	e.POST("/accmgt/acc-blacklist").
		WithJSON(map[string]interface{}{"invalid": "data"}).
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("name")

	// Test get with invalid ID
	e.GET("/accmgt/acc-blacklist/invalid").
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("id")

	// Test get non-existent ID
	e.GET("/accmgt/acc-blacklist/99999").
		Expect().
		Status(http.StatusNotFound)

	// Test get with empty name (route doesn't match, returns 400)
	e.GET("/accmgt/acc-blacklist/name/").
		Expect().
		Status(http.StatusBadRequest) // Gin routing will not match this pattern

	// Test update with invalid ID
	e.PUT("/accmgt/acc-blacklist/invalid").
		WithJSON(apitypes.IDNameType{Name: "test"}).
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("id")

	// Test update with empty name
	e.PUT("/accmgt/acc-blacklist/1").
		WithJSON(apitypes.IDNameType{Name: ""}).
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("name")

	// Test delete with invalid ID
	e.DELETE("/accmgt/acc-blacklist/invalid").
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("id")

	t.Log("AccBlacklist validation test passed!")
}
