package model

// ActivationRequest represents the payload for account activation
type ActivationRequest struct {
	Key       uint64 `json:"key" binding:"required"`
	Token     string `json:"token" binding:"required"`
	Subdomain string `json:"subdomain" binding:"required"`
}

// ActivationResponse represents the response after successful activation
type ActivationResponse struct {
	AccID  uint64 `json:"acc_id"`
	UserID uint64 `json:"user_id"`
	Status string `json:"status"`
}
