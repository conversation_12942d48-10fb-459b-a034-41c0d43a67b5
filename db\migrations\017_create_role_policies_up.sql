-- Create role_policies link table
CREATE TABLE role_policies (
    role_id INTEGER NOT NULL,
    policy_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_role_policies_role_id
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    CONSTRAINT fk_role_policies_policy_id
        FOREIGN KEY (policy_id) REFERENCES policies(id) ON DELETE CASCADE,
    
    -- Composite primary key to prevent duplicates
    PRIMARY KEY (role_id, policy_id)
);

-- Create indexes for better performance
CREATE INDEX idx_role_policies_role_id ON role_policies(role_id);
CREATE INDEX idx_role_policies_policy_id ON role_policies(policy_id);
