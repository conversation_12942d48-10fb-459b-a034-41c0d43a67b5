package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
)

func (c *Client) UserPolicyCreate(ctx context.Context, data model.UserPolicyRequest) error {
	url := c.UrlPfx + accmgtUserPolicy
	_, err := c.Client.Client.Create(ctx, url, data, nil)
	return err
}

func (c *Client) UserPolicyGetByUserID(ctx context.Context, userID uint64) ([]model.UserPolicy, error) {
	url := fmt.Sprintf("%s%s/user/%d", c.UrlPfx, accmgtUserPolicy, userID)
	var result []model.UserPolicy
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) UserPolicyGetByPolicyID(ctx context.Context, policyID uint64) ([]model.UserPolicy, error) {
	url := fmt.Sprintf("%s%s/policy/%d", c.UrlPfx, accmgtUserPolicy, policyID)
	var result []model.UserPolicy
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) UserPolicyDelete(ctx context.Context, userID, policyID uint64) (uint64, error) {
	url := fmt.Sprintf("%s%s/user/%d/policy/%d", c.UrlPfx, accmgtUserPolicy, userID, policyID)
	return c.Client.Client.Delete(ctx, url, nil)
}

func (c *Client) UserPolicyList(ctx context.Context, page apitypes.Page) ([]model.UserPolicy, error) {
	url := c.UrlPfx + accmgtUserPolicies + "?" + page.AsURLParams()
	var result []model.UserPolicy
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
