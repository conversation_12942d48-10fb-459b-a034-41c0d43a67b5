package tests

import (
	"testing"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
)

func TestPolicyPermCreate(t *testing.T) {
	e := getExpect(t)

	// Create test policy and permission first
	policy := apitypes.IDNameType{Name: "TestPolicy"}
	policyResp := e.POST("/policy").
		WithJSON(policy).
		Expect().
		Status(200).
		JSON().Object()
	policyID := uint64(policyResp.Value("id").Number().Raw())
	testData.PolicyIDs = append(testData.PolicyIDs, policyID)

	permission := apitypes.IDNameType{Name: "TestPermission"}
	permissionResp := e.POST("/permission").
		WithJSON(permission).
		Expect().
		Status(200).
		JSON().Object()
	permissionID := uint64(permissionResp.Value("id").Number().Raw())
	testData.PermissionIDs = append(testData.PermissionIDs, permissionID)

	// Create policy-permission link
	policyPerm := model.PolicyPermRequest{
		PolicyID:     policyID,
		PermissionID: permissionID,
	}

	e.POST("/policy-perm").
		WithJSON(policyPerm).
		Expect().
		Status(200).
		JSON().Object().
		ContainsKey("message")
}

func TestPolicyPermCreateValidation(t *testing.T) {
	e := getExpect(t)

	// Test missing policy_id
	policyPerm := model.PolicyPermRequest{
		PermissionID: 1,
	}
	e.POST("/policy-perm").
		WithJSON(policyPerm).
		Expect().
		Status(400)

	// Test missing permission_id
	policyPerm = model.PolicyPermRequest{
		PolicyID: 1,
	}
	e.POST("/policy-perm").
		WithJSON(policyPerm).
		Expect().
		Status(400)

	// Test invalid foreign key
	policyPerm = model.PolicyPermRequest{
		PolicyID:     99999,
		PermissionID: 99999,
	}
	e.POST("/policy-perm").
		WithJSON(policyPerm).
		Expect().
		Status(400)
}

func TestPolicyPermGetByPolicyID(t *testing.T) {
	e := getExpect(t)

	// Create test policy and permission
	policy := apitypes.IDNameType{Name: "TestPolicy2"}
	policyResp := e.POST("/policy").
		WithJSON(policy).
		Expect().
		Status(200).
		JSON().Object()
	policyID := uint64(policyResp.Value("id").Number().Raw())
	testData.PolicyIDs = append(testData.PolicyIDs, policyID)

	permission := apitypes.IDNameType{Name: "TestPermission2"}
	permissionResp := e.POST("/permission").
		WithJSON(permission).
		Expect().
		Status(200).
		JSON().Object()
	permissionID := uint64(permissionResp.Value("id").Number().Raw())
	testData.PermissionIDs = append(testData.PermissionIDs, permissionID)

	// Create policy-permission link
	policyPerm := model.PolicyPermRequest{
		PolicyID:     policyID,
		PermissionID: permissionID,
	}
	e.POST("/policy-perm").
		WithJSON(policyPerm).
		Expect().
		Status(200)

	// Get by policy ID
	resp := e.GET("/policy-perm/policy/{policy_id}", policyID).
		Expect().
		Status(200).
		JSON().Array()

	resp.Length().Ge(1)
	resp.Element(0).Object().
		Value("policy_id").Number().Equal(policyID).
		Value("permission_id").Number().Equal(permissionID)
}

func TestPolicyPermGetByPermissionID(t *testing.T) {
	e := getExpect(t)

	// Create test policy and permission
	policy := apitypes.IDNameType{Name: "TestPolicy3"}
	policyResp := e.POST("/policy").
		WithJSON(policy).
		Expect().
		Status(200).
		JSON().Object()
	policyID := uint64(policyResp.Value("id").Number().Raw())
	testData.PolicyIDs = append(testData.PolicyIDs, policyID)

	permission := apitypes.IDNameType{Name: "TestPermission3"}
	permissionResp := e.POST("/permission").
		WithJSON(permission).
		Expect().
		Status(200).
		JSON().Object()
	permissionID := uint64(permissionResp.Value("id").Number().Raw())
	testData.PermissionIDs = append(testData.PermissionIDs, permissionID)

	// Create policy-permission link
	policyPerm := model.PolicyPermRequest{
		PolicyID:     policyID,
		PermissionID: permissionID,
	}
	e.POST("/policy-perm").
		WithJSON(policyPerm).
		Expect().
		Status(200)

	// Get by permission ID
	resp := e.GET("/policy-perm/permission/{permission_id}", permissionID).
		Expect().
		Status(200).
		JSON().Array()

	resp.Length().Ge(1)
	resp.Element(0).Object().
		Value("policy_id").Number().Equal(policyID).
		Value("permission_id").Number().Equal(permissionID)
}

func TestPolicyPermDelete(t *testing.T) {
	e := getExpect(t)

	// Create test policy and permission
	policy := apitypes.IDNameType{Name: "TestPolicy4"}
	policyResp := e.POST("/policy").
		WithJSON(policy).
		Expect().
		Status(200).
		JSON().Object()
	policyID := uint64(policyResp.Value("id").Number().Raw())
	testData.PolicyIDs = append(testData.PolicyIDs, policyID)

	permission := apitypes.IDNameType{Name: "TestPermission4"}
	permissionResp := e.POST("/permission").
		WithJSON(permission).
		Expect().
		Status(200).
		JSON().Object()
	permissionID := uint64(permissionResp.Value("id").Number().Raw())
	testData.PermissionIDs = append(testData.PermissionIDs, permissionID)

	// Create policy-permission link
	policyPerm := model.PolicyPermRequest{
		PolicyID:     policyID,
		PermissionID: permissionID,
	}
	e.POST("/policy-perm").
		WithJSON(policyPerm).
		Expect().
		Status(200)

	// Delete the link
	e.DELETE("/policy-perm/policy/{policy_id}/permission/{permission_id}", policyID, permissionID).
		Expect().
		Status(200).
		JSON().Object().
		Value("affected_rows").Number().Equal(1)

	// Verify deletion - should return empty array
	e.GET("/policy-perm/policy/{policy_id}", policyID).
		Expect().
		Status(200).
		JSON().Array().
		Length().Equal(0)
}

func TestPolicyPermList(t *testing.T) {
	e := getExpect(t)

	// Get list
	resp := e.GET("/policy-perms").
		WithQuery("page", 0).
		WithQuery("size", 20).
		Expect().
		Status(200).
		JSON().Array()

	// Should be an array (might be empty)
	resp.Length().Ge(0)
}
