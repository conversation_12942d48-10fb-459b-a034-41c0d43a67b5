package dao

import (
	"database/sql"

	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/dbutil"
	"github.com/homewizeAI/errs"
)

const (
	selPolicy = `SELECT id, name FROM policies `
)

// scanRowToPolicy scans a database row into an IDNameType struct
func scanRowToPolicy(scanner dbutil.Scanner) (*apitypes.IDNameType, error) {
	var policy apitypes.IDNameType
	err := scanner.Scan(
		&policy.ID,
		&policy.Name,
	)
	if err != nil {
		return nil, err
	}
	return &policy, nil
}

func PolicyCreate(data apitypes.IDNameType) (uint64, error) {
	query := `
		INSERT INTO policies (
			name
		) VALUES (
			$1
		) RETURNING id
	`

	var id uint64
	err := dbutil.QueryRow(query, data.Name).Scan(&id)

	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, errs.BadInput("relationship")
		}
		if dbutil.ErrIsUniqueViolation(err) {
			return 0, errs.BadInput("name")
		}
		return 0, err
	}

	return id, nil
}

func PolicyGetByID(id uint64) (*apitypes.IDNameType, error) {
	query := selPolicy + `WHERE id = $1`
	row := dbutil.QueryRow(query, id)
	policy, err := scanRowToPolicy(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("policy")
		}
		return nil, err
	}

	return policy, nil
}

func PolicyGetByName(name string) (*apitypes.IDNameType, error) {
	query := selPolicy + `WHERE name = $1`
	row := dbutil.QueryRow(query, name)
	policy, err := scanRowToPolicy(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("policy")
		}
		return nil, err
	}

	return policy, nil
}

func PolicyUpdate(id uint64, data apitypes.IDNameType) (uint64, error) {
	query := `
		UPDATE policies SET
			name = $2,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
	`

	rowsAffected, err := dbutil.ExecGetAffRows(query, id, data.Name)
	if err != nil {
		if dbutil.ErrIsUniqueViolation(err) {
			return 0, errs.BadInput("name")
		}
		return 0, err
	}

	return rowsAffected, nil
}

func PolicyDelete(id uint64) (uint64, error) {
	query := `DELETE FROM policies WHERE id = $1`

	rowsAffected, err := dbutil.ExecGetAffRows(query, id)
	if err != nil {
		return 0, err
	}

	return rowsAffected, nil
}

func PolicyList(page apitypes.Page) ([]apitypes.IDNameType, error) {
	query := selPolicy + `ORDER BY name ` + page.AsSQLLimit()

	rows, err := dbutil.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var policies []apitypes.IDNameType
	for rows.Next() {
		policy, err := scanRowToPolicy(rows)
		if err != nil {
			return nil, err
		}
		policies = append(policies, *policy)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return policies, nil
}
