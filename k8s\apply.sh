#!/bin/bash

# Simple script to apply Kubernetes manifests

echo "🚀 Deploying accmgtms to Kubernetes..."

# Apply ConfigMap first
echo "📝 Creating ConfigMap..."
kubectl apply -f configmap.yaml

# Run migration job
echo "🔄 Running database migrations..."
kubectl apply -f job-db-migrate.yaml

# Wait for migration to complete
echo "⏳ Waiting for migration to complete..."
kubectl wait --for=condition=complete job/accmgtms-migrate --timeout=300s

# Deploy application
echo "🚀 Deploying application..."
kubectl apply -f deployment.yaml

# Create service
echo "🌐 Creating service..."
kubectl apply -f service.yaml

echo "✅ Deployment complete!"
echo "📋 Check status with: kubectl get pods,svc,jobs"
echo "🔍 View logs with: kubectl logs -l app=accmgtms"
