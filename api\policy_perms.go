package api

import (
	"github.com/gin-gonic/gin"
	"github.com/homewizeAI/accmgtms/dao"
	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/errs"
	"github.com/homewizeAI/utils"
)

func PolicyPermCreate(c *gin.Context) {
	var req model.PolicyPermRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		apicommon.RespBadRequest(c, "request")
		return
	}

	err := dao.PolicyPermCreate(req)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		if errs.IsConflict(err) {
			apicommon.RespConflict(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, gin.H{"message": "Policy permission link created successfully"})
}

func PolicyPermGetByPolicyID(c *gin.Context) {
	policyIDStr := c.Param("policy_id")
	policyID, err := utils.AsUint64(policyIDStr)
	if err != nil {
		apicommon.RespBadRequest(c, "policy_id")
		return
	}

	policyPerms, err := dao.PolicyPermGetByPolicyID(policyID)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	// Ensure we return an empty array instead of null
	if policyPerms == nil {
		policyPerms = []model.PolicyPerm{}
	}

	apicommon.RespOk(c, policyPerms)
}

func PolicyPermGetByPermissionID(c *gin.Context) {
	permissionIDStr := c.Param("permission_id")
	permissionID, err := utils.AsUint64(permissionIDStr)
	if err != nil {
		apicommon.RespBadRequest(c, "permission_id")
		return
	}

	policyPerms, err := dao.PolicyPermGetByPermissionID(permissionID)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	// Ensure we return an empty array instead of null
	if policyPerms == nil {
		policyPerms = []model.PolicyPerm{}
	}

	apicommon.RespOk(c, policyPerms)
}

func PolicyPermDelete(c *gin.Context) {
	policyIDStr := c.Param("policy_id")
	permissionIDStr := c.Param("permission_id")

	policyID, err := utils.AsUint64(policyIDStr)
	if err != nil {
		apicommon.RespBadRequest(c, "policy_id")
		return
	}

	permissionID, err := utils.AsUint64(permissionIDStr)
	if err != nil {
		apicommon.RespBadRequest(c, "permission_id")
		return
	}

	affectedRows, err := dao.PolicyPermDelete(policyID, permissionID)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(affectedRows))
}

func PolicyPermList(c *gin.Context) {
	page := apicommon.GetPage(c)
	policyPerms, err := dao.PolicyPermList(page)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	// Ensure we return an empty array instead of null
	if policyPerms == nil {
		policyPerms = []model.PolicyPerm{}
	}

	apicommon.RespOk(c, policyPerms)
}
