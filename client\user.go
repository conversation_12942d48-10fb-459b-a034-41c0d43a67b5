package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
)

func (c *Client) UserCreate(ctx context.Context, data model.User) (uint64, error) {
	url := c.UrlPfx + accmgtUser
	return c.Client.Client.Create(ctx, url, data, nil)
}

func (c *Client) UserGetByID(ctx context.Context, id uint64) (*model.User, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtUser, id)
	var result model.User
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) UserGetByEmail(ctx context.Context, email string) (*model.User, error) {
	url := fmt.Sprintf("%s%s/email/%s", c.UrlPfx, accmgtUser, email)
	var result model.User
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) UserUpdate(ctx context.Context, id uint64, data model.User) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtUser, id)
	return c.Client.Client.Update(ctx, url, data, nil)
}

func (c *Client) UserDelete(ctx context.Context, id uint64) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtUser, id)
	return c.Client.Client.Delete(ctx, url, nil)
}

func (c *Client) UserList(ctx context.Context, page apitypes.Page) ([]model.User, error) {
	url := c.UrlPfx + accmgtUsers + "?" + page.AsURLParams()
	var result []model.User
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
