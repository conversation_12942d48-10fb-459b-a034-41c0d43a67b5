package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
)

func (c *Client) UserCreate(ctx context.Context, data model.User) (uint64, error) {
	url := c.UrlPfx + accmgtUser
	return c.Client.Client.Create(ctx, url, data, nil)
}

func (c *Client) UserGetByID(ctx context.Context, id uint64) (*model.User, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtUser, id)
	var result model.User
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) UserGetByEmail(ctx context.Context, email string) (*model.User, error) {
	url := fmt.Sprintf("%s%s/email/%s", c.UrlPfx, accmgtUser, email)
	var result model.User
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) UserUpdate(ctx context.Context, id uint64, data model.User) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtUser, id)
	return c.Client.Client.Update(ctx, url, data, nil)
}

func (c *Client) UserDelete(ctx context.Context, id uint64) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtUser, id)
	return c.Client.Client.Delete(ctx, url, nil)
}

func (c *Client) UserList(ctx context.Context, page apitypes.Page) ([]model.User, error) {
	url := c.UrlPfx + accmgtUsers + "?" + page.AsURLParams()
	var result []model.User
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// UserGetByKeyAndToken retrieves a user by activation key and token
func (c *Client) UserGetByKeyAndToken(ctx context.Context, key uint64, token string) (*model.User, error) {
	url := fmt.Sprintf("%s%s?key=%d&token=%s", c.UrlPfx, accmgtUserBy, key, token)
	var result model.User
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// UserGetByEmailAndShortName retrieves a user by email and account short name
func (c *Client) UserGetByEmailAndShortName(ctx context.Context, email, shortName string) (*model.User, error) {
	url := fmt.Sprintf("%s%s?email=%s&shortName=%s", c.UrlPfx, accmgtUserBy, email, shortName)
	var result model.User
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// UserGetByAccShortName retrieves a user by account short name only
func (c *Client) UserGetByAccShortName(ctx context.Context, shortName string) (*model.User, error) {
	url := fmt.Sprintf("%s%s?shortName=%s", c.UrlPfx, accmgtUserBy, shortName)
	var result model.User
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) UserIsActive(ctx context.Context, id uint64) (bool, error) {
	url := fmt.Sprintf("%s%s/is-active/%d", c.UrlPfx, accmgtUser, id)
	var result apitypes.BoolType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return false, err
	}
	return result.Value, nil
}
