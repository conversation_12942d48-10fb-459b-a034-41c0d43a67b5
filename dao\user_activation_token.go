package dao

import (
	"database/sql"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/dbutil"
	"github.com/homewizeAI/errs"
	"github.com/homewizeAI/keylib"
)

const (
	selUserActivationToken = `select id, token, user_id, created_at, updated_at from user_activation_tokens `
)

// scanRowToUserActivationToken scans a database row into a UserActivationToken struct
func scanRowToUserActivationToken(scanner dbutil.Scanner) (*model.UserActivationToken, error) {
	var token model.UserActivationToken
	err := scanner.Scan(
		&token.ID,
		&token.Token,
		&token.UserID,
		&token.CreatedAt,
		&token.UpdatedAt,
	)
	if err != nil {
		return nil, err
	}
	return &token, nil
}

func UserActivationTokenCreate(userID uint64) (uint64, string, error) {
	return getOrCreateUserActivationToken(userID)
}

// UserActivationTokenGetByID retrieves a user activation token by its ID
func UserActivationTokenGetByID(id uint64) (*model.UserActivationToken, error) {
	query := selUserActivationToken + ` WHERE id = $1 `
	row := dbutil.QueryRow(query, id)
	token, err := scanRowToUserActivationToken(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("user-activation-token")
		}
		return nil, err
	}

	return token, nil
}

// UserActivationTokenGetByToken retrieves a user activation token by its token value
func UserActivationTokenGetByToken(token string) (*model.UserActivationToken, error) {
	query := selUserActivationToken + ` WHERE token = $1 `
	row := dbutil.QueryRow(query, token)
	tokenData, err := scanRowToUserActivationToken(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("user-activation-token")
		}
		return nil, err
	}

	return tokenData, nil
}

// UserActivationTokenGetByUserID retrieves a user activation token by user ID
func UserActivationTokenGetByUserID(userID uint64) (*model.UserActivationToken, error) {
	query := selUserActivationToken + ` WHERE user_id = $1 `
	row := dbutil.QueryRow(query, userID)
	token, err := scanRowToUserActivationToken(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("user-activation-token")
		}
		return nil, err
	}

	return token, nil
}

// UserActivationTokenUpdate updates an existing user activation token
func UserActivationTokenUpdate(id uint64, data model.UserActivationToken) (uint64, error) {
	query := `
		UPDATE user_activation_tokens SET
			token = $1,
			user_id = $2,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = $3
	`

	rowsAffected, err := dbutil.ExecGetAffRows(
		query,
		data.Token,
		data.UserID,
		id,
	)

	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, errs.BadInput("user-id")
		}
		return 0, err
	}

	return rowsAffected, nil
}

// UserActivationTokenDelete deletes a user activation token by ID
func UserActivationTokenDelete(id uint64) (uint64, error) {
	query := `DELETE FROM user_activation_tokens WHERE id = $1`

	rowsAffected, err := dbutil.ExecGetAffRows(query, id)
	if err != nil {
		return 0, err
	}

	return rowsAffected, nil
}

// generateAndInsertToken generates a secure token and inserts it into the user_activation_tokens table
// Returns the primary key, generated token, and error
func generateAndInsertToken(userID uint64) (uint64, string, error) {
	return generateAndInsertTokenTx(nil, userID)
}

// generateAndInsertTokenTx generates a secure token and inserts it into the user_activation_tokens table with optional transaction
// Returns the primary key, generated token, and error
func generateAndInsertTokenTx(tx *dbutil.Transaction, userID uint64) (uint64, string, error) {
	// Generate a secure token using keylib
	token := keylib.NewSecureKey64()

	query := `
		INSERT INTO user_activation_tokens (
			token,
			user_id
		) VALUES (
			$1,$2
		) RETURNING id
	`

	var id uint64
	var err error

	if tx != nil {
		id, err = tx.ExecGetID(query, token, userID)
	} else {
		err = dbutil.QueryRow(query, token, userID).Scan(&id)
	}

	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, "", errs.BadInput("user-id")
		}
		return 0, "", err
	}

	return id, token, nil
}

// getOrCreateUserActivationToken checks if a user activation token exists for the given userID
// If no record exists, it creates a new one using generateAndInsertToken
// If a record exists, it returns a conflict error
// Returns the primary key, generated token, and error
func getOrCreateUserActivationToken(userID uint64) (uint64, string, error) {
	return getOrCreateUserActivationTokenTx(nil, userID)
}

// getOrCreateUserActivationTokenTx checks if a user activation token exists for the given userID with optional transaction
// If no record exists, it creates a new one using generateAndInsertTokenTx
// If a record exists, it returns a conflict error
// Returns the primary key, generated token, and error
func getOrCreateUserActivationTokenTx(tx *dbutil.Transaction, userID uint64) (uint64, string, error) {
	// First, check if a token already exists for this user
	_, err := UserActivationTokenGetByUserID(userID)
	if err != nil {
		// If the error is "not found", that's expected - we can create a new token
		if errs.IsNotFound(err) {
			// No existing token, create a new one
			return generateAndInsertTokenTx(tx, userID)
		}
		// Some other error occurred
		return 0, "", err
	}

	// If we reach here, a token already exists for this user
	// Return a conflict error
	return 0, "", errs.Conflict("token-exists")
}
