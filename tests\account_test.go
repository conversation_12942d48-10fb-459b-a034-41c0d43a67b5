package tests

import (
	"fmt"
	"net/http"
	"testing"
	"time"
)

func TestAccountCreate(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)
	account := createTestAccount()

	// Test successful account creation
	resp := e.POST("/accmgt/account").
		WithJSON(account).
		Expect().
		Status(http.StatusOK).
		JSON()

	// Verify response structure
	resp.Object().ContainsKey("id")
	accountID := uint64(resp.Object().Value("id").Number().Raw())
	testData.AccountIDs = append(testData.AccountIDs, accountID)

	// Test duplicate short name
	e.POST("/accmgt/account").
		WithJSON(account).
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("short-name")
}

func TestAccountGetByID(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)
	// Create test account
	account := createTestAccount()
	resp := e.POST("/accmgt/account").
		WithJSON(account).
		Expect().
		Status(http.StatusOK).
		JSON()

	accountID := uint64(resp.Object().Value("id").Number().Raw())
	testData.AccountIDs = append(testData.AccountIDs, accountID)

	// Test successful retrieval
	getResp := e.GET(fmt.Sprintf("/accmgt/account/%d", accountID)).
		Expect().
		Status(http.StatusOK).
		JSON()

	// Verify account data
	getResp.Object().Value("name_registered").String().IsEqual(account.NameRegistered)
	getResp.Object().Value("name_short").String().IsEqual(account.NameShort)
	getResp.Object().Value("phone_number").String().IsEqual(account.PhoneNumber)

	// Test invalid ID
	e.GET("/accmgt/account/invalid").
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("id")

	// Test non-existent account
	e.GET("/accmgt/account/99999").
		Expect().
		Status(http.StatusNotFound)
}

func TestAccountGetByShortName(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)
	// Create test account
	account := createTestAccount()
	resp := e.POST("/accmgt/account").
		WithJSON(account).
		Expect().
		Status(http.StatusOK).
		JSON()

	accountID := uint64(resp.Object().Value("id").Number().Raw())
	testData.AccountIDs = append(testData.AccountIDs, accountID)

	// Test successful retrieval by short name
	getResp := e.GET(fmt.Sprintf("/accmgt/account/short/%s", account.NameShort)).
		Expect().
		Status(http.StatusOK).
		JSON()

	// Verify account data
	getResp.Object().Value("name_registered").String().IsEqual(account.NameRegistered)
	getResp.Object().Value("name_short").String().IsEqual(account.NameShort)

	// Test non-existent short name
	e.GET("/accmgt/account/short/nonexistent").
		Expect().
		Status(http.StatusNotFound)
}

func TestAccountUpdate(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)
	// Create test account
	account := createTestAccount()
	resp := e.POST("/accmgt/account").
		WithJSON(account).
		Expect().
		Status(http.StatusOK).
		JSON()

	accountID := uint64(resp.Object().Value("id").Number().Raw())
	testData.AccountIDs = append(testData.AccountIDs, accountID)

	// Update account data
	account.NameRegistered = "Updated Company Name"
	account.PhoneNumber = "555-9999"

	// Test successful update
	updateResp := e.PUT(fmt.Sprintf("/accmgt/account/%d", accountID)).
		WithJSON(account).
		Expect().
		Status(http.StatusOK).
		JSON()

	// Verify affected rows
	updateResp.Object().Value("affected_rows").Number().IsEqual(1)

	// Verify the update by retrieving the account
	getResp := e.GET(fmt.Sprintf("/accmgt/account/%d", accountID)).
		Expect().
		Status(http.StatusOK).
		JSON()

	getResp.Object().Value("name_registered").String().IsEqual("Updated Company Name")
	getResp.Object().Value("phone_number").String().IsEqual("555-9999")

	// Test invalid ID
	e.PUT("/accmgt/account/invalid").
		WithJSON(account).
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("id")
}

func TestAccountDelete(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)
	// Create test account
	account := createTestAccount()
	resp := e.POST("/accmgt/account").
		WithJSON(account).
		Expect().
		Status(http.StatusOK).
		JSON()

	accountID := uint64(resp.Object().Value("id").Number().Raw())
	testData.AccountIDs = append(testData.AccountIDs, accountID)

	// Test successful deletion
	deleteResp := e.DELETE(fmt.Sprintf("/accmgt/account/%d", accountID)).
		Expect().
		Status(http.StatusOK).
		JSON()

	// Verify affected rows
	deleteResp.Object().Value("affected_rows").Number().IsEqual(1)

	// Verify account is deleted
	e.GET(fmt.Sprintf("/accmgt/account/%d", accountID)).
		Expect().
		Status(http.StatusNotFound)

	// Remove from cleanup list since it's already deleted
	testData.AccountIDs = testData.AccountIDs[:len(testData.AccountIDs)-1]

	// Test invalid ID
	e.DELETE("/accmgt/account/invalid").
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("id")
}

func TestAccountList(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)
	// Create multiple test accounts
	account1 := createTestAccount()
	time.Sleep(1 * time.Millisecond) // Ensure different timestamps
	account2 := createTestAccount()

	resp1 := e.POST("/accmgt/account").
		WithJSON(account1).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp2 := e.POST("/accmgt/account").
		WithJSON(account2).
		Expect().
		Status(http.StatusOK).
		JSON()

	accountID1 := uint64(resp1.Object().Value("id").Number().Raw())
	accountID2 := uint64(resp2.Object().Value("id").Number().Raw())
	testData.AccountIDs = append(testData.AccountIDs, accountID1, accountID2)

	// Test account list
	listResp := e.GET("/accmgt/accounts").
		WithQuery("page", 0).
		WithQuery("size", 20).
		Expect().
		Status(http.StatusOK).
		JSON()

	// Verify response is an array
	listResp.Array().Length().Ge(2)
}

func TestAccountCount(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)
	// Get initial count
	initialResp := e.GET("/accmgt/accounts/count").
		Expect().
		Status(http.StatusOK).
		JSON()

	initialCount := initialResp.Object().Value("count").Number().Raw()

	// Create test account
	account := createTestAccount()
	resp := e.POST("/accmgt/account").
		WithJSON(account).
		Expect().
		Status(http.StatusOK).
		JSON()

	accountID := uint64(resp.Object().Value("id").Number().Raw())
	testData.AccountIDs = append(testData.AccountIDs, accountID)

	// Verify count increased
	newResp := e.GET("/accmgt/accounts/count").
		Expect().
		Status(http.StatusOK).
		JSON()

	newResp.Object().Value("count").Number().IsEqual(initialCount + 1)
}
