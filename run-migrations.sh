#!/bin/bash

# Simple script to run SQL migrations using Go
# Usage: ./run-migrations.sh [up|down]

set -e  # Exit on any error

# Parse command line arguments
COMMAND=${1:-up}

if [[ "$COMMAND" != "up" && "$COMMAND" != "down" ]]; then
    echo "Usage: $0 [up|down]"
    echo "  up   - Apply migrations (default)"
    echo "  down - Rollback migrations"
    exit 1
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Go is available
if ! command -v go &> /dev/null; then
    print_error "Go is not installed or not in PATH"
    exit 1
fi

# Check if migrate.go exists
if [ ! -f "tools/migrate.go" ]; then
    print_error "tools/migrate.go file not found"
    exit 1
fi

# Run the Go migration program
print_info "Running $COMMAND migrations using Go..."

if go run tools/migrate.go "$COMMAND"; then
    print_success "Migration operation completed successfully!"
else
    print_error "Migration operation failed!"
    exit 1
fi
