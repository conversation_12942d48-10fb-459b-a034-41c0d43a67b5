package tests

import (
	"fmt"
	"strconv"
	"testing"
	"time"

	"github.com/homewizeAI/apitypes"
)

func TestPermissionCreate(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	timestamp := time.Now().Unix()
	permissionName := fmt.Sprintf("Test Permission Create %d", timestamp)

	permission := apitypes.IDNameType{
		Name: permissionName,
	}

	resp := e.POST("/accmgt/permission").
		WithJSON(permission).
		Expect().
		Status(200).
		JSON().Object()

	resp.ContainsKey("id")
	id := uint64(resp.Value("id").Number().Raw())
	testData.PermissionIDs = append(testData.PermissionIDs, id)

	t.Logf("Created permission with ID: %d", id)
}

func TestPermissionGetByID(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// First create a permission
	timestamp := time.Now().Unix()
	permissionName := fmt.Sprintf("Test Permission GetByID %d", timestamp)

	permission := apitypes.IDNameType{
		Name: permissionName,
	}

	createResp := e.POST("/accmgt/permission").
		WithJSON(permission).
		Expect().
		Status(200).
		JSON().Object()

	id := uint64(createResp.Value("id").Number().Raw())
	testData.PermissionIDs = append(testData.PermissionIDs, id)

	// Now get the permission by ID
	resp := e.GET("/accmgt/permission/" + strconv.FormatUint(id, 10)).
		Expect().
		Status(200).
		JSON().Object()

	resp.Value("id").Number().IsEqual(float64(id))
	resp.Value("name").String().IsEqual(permissionName)

	t.Logf("Successfully retrieved permission by ID: %d", id)
}

func TestPermissionGetByName(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// First create a permission
	timestamp := time.Now().Unix()
	permissionName := fmt.Sprintf("Test Permission GetByName %d", timestamp)

	permission := apitypes.IDNameType{
		Name: permissionName,
	}

	createResp := e.POST("/accmgt/permission").
		WithJSON(permission).
		Expect().
		Status(200).
		JSON().Object()

	id := uint64(createResp.Value("id").Number().Raw())
	testData.PermissionIDs = append(testData.PermissionIDs, id)

	// Now get the permission by name
	resp := e.GET("/accmgt/permission/name/" + permissionName).
		Expect().
		Status(200).
		JSON().Object()

	resp.Value("id").Number().IsEqual(float64(id))
	resp.Value("name").String().IsEqual(permissionName)

	t.Logf("Successfully retrieved permission by name: %s", permissionName)
}

func TestPermissionUpdate(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// First create a permission
	timestamp := time.Now().Unix()
	originalName := fmt.Sprintf("Test Permission Update Original %d", timestamp)

	permission := apitypes.IDNameType{
		Name: originalName,
	}

	createResp := e.POST("/accmgt/permission").
		WithJSON(permission).
		Expect().
		Status(200).
		JSON().Object()

	id := uint64(createResp.Value("id").Number().Raw())
	testData.PermissionIDs = append(testData.PermissionIDs, id)

	// Update the permission
	updatedName := fmt.Sprintf("Test Permission Update Modified %d", timestamp)
	updatePermission := apitypes.IDNameType{
		Name: updatedName,
	}

	updateResp := e.PUT("/accmgt/permission/" + strconv.FormatUint(id, 10)).
		WithJSON(updatePermission).
		Expect().
		Status(200).
		JSON().Object()

	updateResp.Value("affected_rows").Number().IsEqual(1)

	// Verify the update
	getResp := e.GET("/accmgt/permission/" + strconv.FormatUint(id, 10)).
		Expect().
		Status(200).
		JSON().Object()

	getResp.Value("name").String().IsEqual(updatedName)

	t.Logf("Successfully updated permission ID: %d", id)
}

func TestPermissionDelete(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// First create a permission
	timestamp := time.Now().Unix()
	permissionName := fmt.Sprintf("Test Permission Delete %d", timestamp)

	permission := apitypes.IDNameType{
		Name: permissionName,
	}

	createResp := e.POST("/accmgt/permission").
		WithJSON(permission).
		Expect().
		Status(200).
		JSON().Object()

	id := uint64(createResp.Value("id").Number().Raw())
	testData.PermissionIDs = append(testData.PermissionIDs, id)

	// Delete the permission
	deleteResp := e.DELETE("/accmgt/permission/" + strconv.FormatUint(id, 10)).
		Expect().
		Status(200).
		JSON().Object()

	deleteResp.Value("affected_rows").Number().IsEqual(1)

	// Verify the permission is deleted
	e.GET("/accmgt/permission/" + strconv.FormatUint(id, 10)).
		Expect().
		Status(404)

	t.Logf("Successfully deleted permission ID: %d", id)
}

func TestPermissionList(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	// Create multiple permissions
	timestamp := time.Now().Unix()
	for i := 0; i < 3; i++ {
		permissionName := fmt.Sprintf("Test Permission List %d-%d", timestamp, i)

		permission := apitypes.IDNameType{
			Name: permissionName,
		}

		createResp := e.POST("/accmgt/permission").
			WithJSON(permission).
			Expect().
			Status(200).
			JSON().Object()

		id := uint64(createResp.Value("id").Number().Raw())
		testData.PermissionIDs = append(testData.PermissionIDs, id)
	}

	// List permissions
	resp := e.GET("/accmgt/permissions").
		WithQuery("page", 0).
		WithQuery("size", 10).
		Expect().
		Status(200).
		JSON().Array()

	resp.Length().Ge(3)

	t.Logf("Successfully listed permissions")
}

func TestPermissionValidation(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	t.Run("CreateWithEmptyName", func(t *testing.T) {
		permission := apitypes.IDNameType{
			Name: "",
		}

		e.POST("/accmgt/permission").
			WithJSON(permission).
			Expect().
			Status(400)
	})

	t.Run("CreateDuplicateName", func(t *testing.T) {
		timestamp := time.Now().Unix()
		permissionName := fmt.Sprintf("Test Permission Duplicate %d", timestamp)

		permission := apitypes.IDNameType{
			Name: permissionName,
		}

		// Create first permission
		createResp := e.POST("/accmgt/permission").
			WithJSON(permission).
			Expect().
			Status(200).
			JSON().Object()

		id := uint64(createResp.Value("id").Number().Raw())
		testData.PermissionIDs = append(testData.PermissionIDs, id)

		// Try to create duplicate
		e.POST("/accmgt/permission").
			WithJSON(permission).
			Expect().
			Status(400)
	})

	t.Run("GetNonExistentPermission", func(t *testing.T) {
		e.GET("/accmgt/permission/99999").
			Expect().
			Status(404)
	})

	t.Run("GetByInvalidID", func(t *testing.T) {
		e.GET("/accmgt/permission/invalid").
			Expect().
			Status(400)
	})

	t.Run("UpdateNonExistentPermission", func(t *testing.T) {
		permission := apitypes.IDNameType{
			Name: "Non-existent Permission",
		}

		e.PUT("/accmgt/permission/99999").
			WithJSON(permission).
			Expect().
			Status(200).
			JSON().Object().
			Value("affected_rows").Number().IsEqual(0)
	})

	t.Run("DeleteNonExistentPermission", func(t *testing.T) {
		e.DELETE("/accmgt/permission/99999").
			Expect().
			Status(200).
			JSON().Object().
			Value("affected_rows").Number().IsEqual(0)
	})

	t.Logf("All permission validation tests passed!")
}
