package tests

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/homewizeAI/accmgtms/model"
)

// Test UsrBy endpoint with different query parameter combinations
func TestUsrBy(t *testing.T) {
	defer cleanupTestData()
	e := getExpect(t)

	timestamp := time.Now().Unix()

	// Create test account
	account := model.Account{
		NameRegistered: fmt.Sprintf("UsrBy Test Company %d", timestamp),
		NameShort:      fmt.Sprintf("usrbyco%d", timestamp),
		PhoneNumber:    "555-1234",
	}

	accResp := e.POST("/accmgt/account").
		WithJSON(account).
		Expect().
		Status(http.StatusOK).
		JSON()

	accID := uint64(accResp.Object().Value("id").Number().Raw())
	testData.AccountIDs = append(testData.AccountIDs, accID)

	// Create test user
	user := model.User{
		AccID:     accID,
		RoleID:    10, // Admin role
		NameFirst: "<PERSON>",
		NameLast:  "Doe",
		Email:     fmt.Sprintf("<EMAIL>", timestamp),
	}

	userResp := e.POST("/accmgt/user").
		WithJSON(user).
		Expect().
		Status(http.StatusOK).
		JSON()

	userID := uint64(userResp.Object().Value("id").Number().Raw())
	testData.UserIDs = append(testData.UserIDs, userID)

	// Create user activation token
	tokenResp := e.POST(fmt.Sprintf("/accmgt/user-activation-token/%d", userID)).
		WithJSON(struct{}{}).
		Expect().
		Status(http.StatusOK).
		JSON()

	activationKey := uint64(tokenResp.Object().Value("id").Number().Raw())
	activationToken := tokenResp.Object().Value("token").String().Raw()

	t.Logf("Created test data - AccID: %d, UserID: %d, Key: %d, Token: %s", accID, userID, activationKey, activationToken)

	// Test 1: Get user by key & token
	t.Run("GetByKeyAndToken", func(t *testing.T) {
		resp := e.GET("/accmgt/user/by").
			WithQuery("key", activationKey).
			WithQuery("token", activationToken).
			Expect().
			Status(http.StatusOK).
			JSON()

		resp.Object().Value("id").Number().IsEqual(float64(userID))
		resp.Object().Value("email").String().IsEqual(user.Email)
		resp.Object().Value("acc_id").Number().IsEqual(float64(accID))
		t.Logf("Successfully retrieved user by key & token")
	})

	// Test 2: Get user by email & shortName
	t.Run("GetByEmailAndShortName", func(t *testing.T) {
		resp := e.GET("/accmgt/user/by").
			WithQuery("email", user.Email).
			WithQuery("shortName", account.NameShort).
			Expect().
			Status(http.StatusOK).
			JSON()

		resp.Object().Value("id").Number().IsEqual(float64(userID))
		resp.Object().Value("email").String().IsEqual(user.Email)
		resp.Object().Value("acc_id").Number().IsEqual(float64(accID))
		t.Logf("Successfully retrieved user by email & shortName")
	})

	// Test 3: Get user by shortName only
	t.Run("GetByShortNameOnly", func(t *testing.T) {
		resp := e.GET("/accmgt/user/by").
			WithQuery("shortName", account.NameShort).
			Expect().
			Status(http.StatusOK).
			JSON()

		resp.Object().Value("id").Number().IsEqual(float64(userID))
		resp.Object().Value("email").String().IsEqual(user.Email)
		resp.Object().Value("acc_id").Number().IsEqual(float64(accID))
		t.Logf("Successfully retrieved user by shortName only")
	})

	t.Logf("All UsrBy retrieval tests passed!")
}

// Test UsrBy validation scenarios
func TestUsrByValidation(t *testing.T) {
	e := getExpect(t)

	// Test 1: No parameters provided
	t.Run("NoParameters", func(t *testing.T) {
		e.GET("/accmgt/user/by").
			Expect().
			Status(http.StatusBadRequest).
			JSON().Object().Value("reason").String().Contains("missing-parameters")
	})

	// Test 2: Multiple combinations provided (key+token AND email+shortName)
	t.Run("MultipleCombinations", func(t *testing.T) {
		e.GET("/accmgt/user/by").
			WithQuery("key", "123").
			WithQuery("token", "abc").
			WithQuery("email", "<EMAIL>").
			WithQuery("shortName", "testco").
			Expect().
			Status(http.StatusBadRequest).
			JSON().Object().Value("reason").String().Contains("multiple-combinations")
	})

	// Test 3: Invalid key format
	t.Run("InvalidKey", func(t *testing.T) {
		e.GET("/accmgt/user/by").
			WithQuery("key", "invalid").
			WithQuery("token", "abc").
			Expect().
			Status(http.StatusBadRequest).
			JSON().Object().Value("reason").String().Contains("key")
	})

	// Test 4: Non-existent user (key & token)
	t.Run("NonExistentUserKeyToken", func(t *testing.T) {
		e.GET("/accmgt/user/by").
			WithQuery("key", "99999").
			WithQuery("token", "nonexistent").
			Expect().
			Status(http.StatusNotFound).
			JSON().Object().Value("reason").String().Contains("user")
	})

	// Test 5: Non-existent user (email & shortName)
	t.Run("NonExistentUserEmailShortName", func(t *testing.T) {
		e.GET("/accmgt/user/by").
			WithQuery("email", "<EMAIL>").
			WithQuery("shortName", "nonexistentco").
			Expect().
			Status(http.StatusNotFound).
			JSON().Object().Value("reason").String().Contains("user")
	})

	// Test 6: Non-existent user (shortName only)
	t.Run("NonExistentUserShortNameOnly", func(t *testing.T) {
		e.GET("/accmgt/user/by").
			WithQuery("shortName", "nonexistentco").
			Expect().
			Status(http.StatusNotFound).
			JSON().Object().Value("reason").String().Contains("user")
	})

	t.Logf("All UsrBy validation tests passed!")
}
