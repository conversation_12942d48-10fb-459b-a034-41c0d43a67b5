package client

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
	"github.com/jarcoal/httpmock"
)

func TestAccStateReasonCreate(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"POST",
		"http://localhost:3000/accmgt/acc-state-reason",
		func(req *http.Request) (*http.Response, error) {
			var accStateReason model.AccStateReason
			err := json.NewDecoder(req.Body).Decode(&accStateReason)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			var resp apitypes.IDType
			resp.ID = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	var accStateReason model.AccStateReason
	accStateReason.AccStateID = 1
	accStateReason.Reason = "Account suspended due to policy violation"
	id, err := c.AccStateReasonCreate(context.Background(), accStateReason)
	if err != nil {
		t.Error(err)
	}
	if id != 1 {
		t.Errorf("Expected id 1, got %d", id)
	}
}

func TestAccStateReasonGetByID(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/acc-state-reason/1",
		func(req *http.Request) (*http.Response, error) {
			accStateReason := model.AccStateReason{
				ID:         1,
				AccStateID: 1,
				Reason:     "Account suspended due to policy violation",
			}
			return httpmock.NewJsonResponse(http.StatusOK, accStateReason)
		},
	)

	accStateReason, err := c.AccStateReasonGetByID(context.Background(), 1)
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if accStateReason == nil {
		t.Error("Expected acc state reason response, got nil")
	}
}

func TestAccStateReasonUpdate(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"PUT",
		"http://localhost:3000/accmgt/acc-state-reason/1",
		func(req *http.Request) (*http.Response, error) {
			var accStateReason model.AccStateReason
			err := json.NewDecoder(req.Body).Decode(&accStateReason)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			var resp apitypes.AffectedRowsType
			resp.AffectedRows = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	var accStateReason model.AccStateReason
	accStateReason.AccStateID = 1
	accStateReason.Reason = "Account suspended due to security concerns"
	rowsAffected, err := c.AccStateReasonUpdate(context.Background(), 1, accStateReason)
	if err != nil {
		t.Error(err)
	}
	if rowsAffected != 1 {
		t.Errorf("Expected 1 row affected, got %d", rowsAffected)
	}
}

func TestAccStateReasonDelete(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"DELETE",
		"http://localhost:3000/accmgt/acc-state-reason/1",
		func(req *http.Request) (*http.Response, error) {
			var resp apitypes.AffectedRowsType
			resp.AffectedRows = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	rowsAffected, err := c.AccStateReasonDelete(context.Background(), 1)
	if err != nil {
		t.Error(err)
	}
	if rowsAffected != 1 {
		t.Errorf("Expected 1 row affected, got %d", rowsAffected)
	}
}

func TestAccStateReasonList(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/acc-state-reasons",
		func(req *http.Request) (*http.Response, error) {
			accStateReasons := []model.AccStateReason{
				{ID: 1, AccStateID: 1, Reason: "Account suspended due to policy violation"},
				{ID: 2, AccStateID: 2, Reason: "Account closed by user request"},
			}
			return httpmock.NewJsonResponse(http.StatusOK, accStateReasons)
		},
	)

	page := apitypes.Page{Page: 0, Size: 20}
	accStateReasons, err := c.AccStateReasonList(context.Background(), page)
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if accStateReasons == nil {
		t.Error("Expected acc state reasons response, got nil")
	}
}
