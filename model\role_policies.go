package model

import (
	"time"
)

// RolePolicy represents a link between a role and a policy
type RolePolicy struct {
	RoleID    uint64    `json:"role_id" db:"role_id"`
	PolicyID  uint64    `json:"policy_id" db:"policy_id"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// RolePolicyRequest represents the request structure for creating role-policy links
type RolePolicyRequest struct {
	RoleID   uint64 `json:"role_id" binding:"required"`
	PolicyID uint64 `json:"policy_id" binding:"required"`
}
