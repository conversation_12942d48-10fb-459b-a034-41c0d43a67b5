package client

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
	"github.com/jarcoal/httpmock"
)

func TestUserActivationTokenCreate(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	// Mock the POST request for creation
	httpmock.RegisterResponder(
		"POST",
		"http://localhost:3000/accmgt/user-activation-token/1",
		func(req *http.Request) (*http.Response, error) {
			var resp apitypes.IDType
			resp.ID = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	// Mock the GET request for retrieving the created token
	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/user-activation-token/user/1",
		func(req *http.Request) (*http.Response, error) {
			token := model.UserActivationToken{
				ID:     1,
				Token:  "test-token-123",
				UserID: 1,
			}
			return httpmock.NewJsonResponse(http.StatusOK, token)
		},
	)

	token, err := c.UserActivationTokenCreate(context.Background(), 1)
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if token == nil {
		t.Error("Expected token response, got nil")
	}
}

func TestUserActivationTokenGetByID(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/user-activation-token/1",
		func(req *http.Request) (*http.Response, error) {
			token := model.UserActivationToken{
				ID:     1,
				Token:  "test-token-123",
				UserID: 1,
			}
			return httpmock.NewJsonResponse(http.StatusOK, token)
		},
	)

	token, err := c.UserActivationTokenGetByID(context.Background(), 1)
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if token == nil {
		t.Error("Expected token response, got nil")
	}
}

func TestUserActivationTokenGetByToken(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/user-activation-token/token/test-token-123",
		func(req *http.Request) (*http.Response, error) {
			token := model.UserActivationToken{
				ID:     1,
				Token:  "test-token-123",
				UserID: 1,
			}
			return httpmock.NewJsonResponse(http.StatusOK, token)
		},
	)

	token, err := c.UserActivationTokenGetByToken(context.Background(), "test-token-123")
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if token == nil {
		t.Error("Expected token response, got nil")
	}
}

func TestUserActivationTokenGetByUserID(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/user-activation-token/user/1",
		func(req *http.Request) (*http.Response, error) {
			token := model.UserActivationToken{
				ID:     1,
				Token:  "test-token-123",
				UserID: 1,
			}
			return httpmock.NewJsonResponse(http.StatusOK, token)
		},
	)

	token, err := c.UserActivationTokenGetByUserID(context.Background(), 1)
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if token == nil {
		t.Error("Expected token response, got nil")
	}
}

func TestUserActivationTokenUpdate(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"PUT",
		"http://localhost:3000/accmgt/user-activation-token/1",
		func(req *http.Request) (*http.Response, error) {
			var token model.UserActivationToken
			err := json.NewDecoder(req.Body).Decode(&token)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			var resp apitypes.AffectedRowsType
			resp.AffectedRows = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	var token model.UserActivationToken
	token.Token = "updated-token-456"
	token.UserID = 1
	rowsAffected, err := c.UserActivationTokenUpdate(context.Background(), 1, token)
	if err != nil {
		t.Error(err)
	}
	if rowsAffected != 1 {
		t.Errorf("Expected 1 row affected, got %d", rowsAffected)
	}
}

func TestUserActivationTokenDelete(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"DELETE",
		"http://localhost:3000/accmgt/user-activation-token/1",
		func(req *http.Request) (*http.Response, error) {
			var resp apitypes.AffectedRowsType
			resp.AffectedRows = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	rowsAffected, err := c.UserActivationTokenDelete(context.Background(), 1)
	if err != nil {
		t.Error(err)
	}
	if rowsAffected != 1 {
		t.Errorf("Expected 1 row affected, got %d", rowsAffected)
	}
}
