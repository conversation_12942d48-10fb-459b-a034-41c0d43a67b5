package model

import (
	"time"
)

// User represents a user in the system
type User struct {
	ID                 uint64    `json:"id" db:"id"`
	AccID              uint64    `json:"acc_id" db:"acc_id"`
	RoleID             uint64    `json:"role_id" db:"role_id"`
	UserStateID        uint64    `json:"user_state_id" db:"user_state_id"`
	NameFirst          string    `json:"name_first" db:"name_first"`
	NameMiddle         *string   `json:"name_middle" db:"name_middle"`
	NameLast           string    `json:"name_last" db:"name_last"`
	Email              string    `json:"email" db:"email"`
	AddrHomeLine1      *string   `json:"addr_home_line1" db:"addr_home_line1"`
	AddrHomeLine2      *string   `json:"addr_home_line2" db:"addr_home_line2"`
	AddrHomeCity       *string   `json:"addr_home_city" db:"addr_home_city"`
	AddrHomeState      *string   `json:"addr_home_state" db:"addr_home_state"`
	AddrHomeCountry    *string   `json:"addr_home_country" db:"addr_home_country"`
	AddrHomePostalCode *string   `json:"addr_home_postal_code" db:"addr_home_postal_code"`
	AddrWorkLine1      *string   `json:"addr_work_line1" db:"addr_work_line1"`
	AddrWorkLine2      *string   `json:"addr_work_line2" db:"addr_work_line2"`
	AddrWorkCity       *string   `json:"addr_work_city" db:"addr_work_city"`
	AddrWorkState      *string   `json:"addr_work_state" db:"addr_work_state"`
	AddrWorkCountry    *string   `json:"addr_work_country" db:"addr_work_country"`
	AddrWorkPostalCode *string   `json:"addr_work_postal_code" db:"addr_work_postal_code"`
	PhoneHomeCC        *string   `json:"phone_home_cc" db:"phone_home_cc"`
	PhoneHomeNumber    *string   `json:"phone_home_number" db:"phone_home_number"`
	PhoneWorkCC        *string   `json:"phone_work_cc" db:"phone_work_cc"`
	PhoneWorkNumber    *string   `json:"phone_work_number" db:"phone_work_number"`
	PhoneMobileCC      *string   `json:"phone_mobile_cc" db:"phone_mobile_cc"`
	PhoneMobileNumber  *string   `json:"phone_mobile_number" db:"phone_mobile_number"`
	CreatedAt          time.Time `json:"created_at" db:"created_at"`
	UpdatedAt          time.Time `json:"updated_at" db:"updated_at"`
}
