package tests

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
)

// Test Role CRUD operations
func TestRoleOperations(t *testing.T) {
	defer cleanupTestData()
	e := getExpect(t)

	// Create test role
	timestamp := time.Now().Unix()
	role := model.Role{
		Name: fmt.Sprintf("TestRole%d", timestamp),
	}

	// Test role creation
	resp := e.POST("/accmgt/role").
		WithJSON(role).
		Expect().
		Status(http.StatusOK).
		JSON()

	resp.Object().ContainsKey("id")
	roleID := uint64(resp.Object().Value("id").Number().Raw())
	testData.RoleIDs = append(testData.RoleIDs, roleID)
	t.Logf("Created role with ID: %d", roleID)

	// Test role retrieval by ID
	getResp := e.GET(fmt.Sprintf("/accmgt/role/%d", roleID)).
		Expect().
		Status(http.StatusOK).
		JSON()
	getResp.Object().Value("name").String().IsEqual(role.Name)

	// Test role retrieval by name
	getByNameResp := e.GET(fmt.Sprintf("/accmgt/role/name/%s", role.Name)).
		Expect().
		Status(http.StatusOK).
		JSON()
	getByNameResp.Object().Value("name").String().IsEqual(role.Name)

	// Test role update
	role.Name = "UpdatedRoleName"
	updateResp := e.PUT(fmt.Sprintf("/accmgt/role/%d", roleID)).
		WithJSON(role).
		Expect().
		Status(http.StatusOK).
		JSON()
	updateResp.Object().Value("affected_rows").Number().IsEqual(1)

	// Test role list
	listResp := e.GET("/accmgt/roles").
		WithQuery("page", 0).
		WithQuery("size", 20).
		Expect().
		Status(http.StatusOK).
		JSON()
	listResp.Array().Length().Ge(1)

	t.Log("Role operations test passed!")
}

// Test User CRUD operations
func TestUserOperations(t *testing.T) {
	defer cleanupTestData()
	e := getExpect(t)

	// Create test account first
	timestamp := time.Now().Unix()
	account := model.Account{
		NameRegistered: fmt.Sprintf("User Test Company %d", timestamp),
		NameShort:      fmt.Sprintf("userco%d", timestamp),
		PhoneNumber:    "555-5678",
	}

	accResp := e.POST("/accmgt/account").
		WithJSON(account).
		Expect().
		Status(http.StatusOK).
		JSON()

	accountID := uint64(accResp.Object().Value("id").Number().Raw())
	testData.AccountIDs = append(testData.AccountIDs, accountID)

	// Create test user
	user := model.User{
		AccID:       accountID,
		RoleID:      10, // Admin role from seed data
		UserStateID: 10, // Not Validated state from seed data
		NameFirst:   "John",
		NameLast:    "Doe",
		Email:       fmt.Sprintf("<EMAIL>", timestamp),
	}

	// Test user creation
	userResp := e.POST("/accmgt/user").
		WithJSON(user).
		Expect().
		Status(http.StatusOK).
		JSON()

	userResp.Object().ContainsKey("id")
	userID := uint64(userResp.Object().Value("id").Number().Raw())
	testData.UserIDs = append(testData.UserIDs, userID)
	t.Logf("Created user with ID: %d", userID)

	// Test user retrieval by ID
	getUserResp := e.GET(fmt.Sprintf("/accmgt/user/%d", userID)).
		Expect().
		Status(http.StatusOK).
		JSON()
	getUserResp.Object().Value("name_first").String().IsEqual(user.NameFirst)
	getUserResp.Object().Value("email").String().IsEqual(user.Email)

	// Test user retrieval by email
	getUserByEmailResp := e.GET(fmt.Sprintf("/accmgt/user/email/%s", user.Email)).
		Expect().
		Status(http.StatusOK).
		JSON()
	getUserByEmailResp.Object().Value("name_first").String().IsEqual(user.NameFirst)

	// Test user update
	user.NameFirst = "Jane"
	updateUserResp := e.PUT(fmt.Sprintf("/accmgt/user/%d", userID)).
		WithJSON(user).
		Expect().
		Status(http.StatusOK).
		JSON()
	updateUserResp.Object().Value("affected_rows").Number().IsEqual(1)

	// Test user list
	userListResp := e.GET("/accmgt/users").
		WithQuery("page", 0).
		WithQuery("size", 20).
		Expect().
		Status(http.StatusOK).
		JSON()
	userListResp.Array().Length().Ge(1)

	t.Log("User operations test passed!")
}

// Test Signup functionality
func TestSignupOperation(t *testing.T) {
	defer cleanupTestData()
	e := getExpect(t)

	// Create signup request
	timestamp := time.Now().Unix()
	
	account := model.Account{
		NameRegistered: fmt.Sprintf("Signup Test Company %d", timestamp),
		NameShort:      fmt.Sprintf("signupco%d", timestamp),
		PhoneNumber:    "555-9999",
	}

	user := model.User{
		NameFirst: "Jane",
		NameLast:  "Smith",
		Email:     fmt.Sprintf("<EMAIL>", timestamp),
	}

	signupRequest := model.SignupRequest{
		Account: account,
		User:    user,
	}

	// Test successful signup
	resp := e.POST("/accmgt/signup").
		WithJSON(signupRequest).
		Expect().
		Status(http.StatusOK).
		JSON()

	// Verify response structure
	resp.Object().ContainsKey("acc_id")
	resp.Object().ContainsKey("user_id")
	resp.Object().ContainsKey("activation_key_id")
	resp.Object().ContainsKey("activation_token")

	accountID := uint64(resp.Object().Value("acc_id").Number().Raw())
	userID := uint64(resp.Object().Value("user_id").Number().Raw())
	activationKeyID := uint64(resp.Object().Value("activation_key_id").Number().Raw())

	// Add to cleanup lists
	testData.AccountIDs = append(testData.AccountIDs, accountID)
	testData.UserIDs = append(testData.UserIDs, userID)
	testData.UserActivationTokenIDs = append(testData.UserActivationTokenIDs, activationKeyID)

	t.Logf("Signup created account ID: %d, user ID: %d", accountID, userID)
	t.Log("Signup operation test passed!")
}

// Test User Activation Token operations
func TestUserActivationTokenOperations(t *testing.T) {
	defer cleanupTestData()
	e := getExpect(t)

	// Create test account and user first
	timestamp := time.Now().Unix()
	account := model.Account{
		NameRegistered: fmt.Sprintf("Token Test Company %d", timestamp),
		NameShort:      fmt.Sprintf("tokenco%d", timestamp),
		PhoneNumber:    "555-7777",
	}

	accResp := e.POST("/accmgt/account").
		WithJSON(account).
		Expect().
		Status(http.StatusOK).
		JSON()

	accountID := uint64(accResp.Object().Value("id").Number().Raw())
	testData.AccountIDs = append(testData.AccountIDs, accountID)

	user := model.User{
		AccID:       accountID,
		RoleID:      10,
		UserStateID: 10,
		NameFirst:   "Token",
		NameLast:    "User",
		Email:       fmt.Sprintf("<EMAIL>", timestamp),
	}

	userResp := e.POST("/accmgt/user").
		WithJSON(user).
		Expect().
		Status(http.StatusOK).
		JSON()

	userID := uint64(userResp.Object().Value("id").Number().Raw())
	testData.UserIDs = append(testData.UserIDs, userID)

	// Test user activation token creation
	tokenResp := e.POST(fmt.Sprintf("/accmgt/user-activation-token/%d", userID)).
		Expect().
		Status(http.StatusOK).
		JSON()

	tokenResp.Object().ContainsKey("id")
	tokenResp.Object().ContainsKey("token")
	tokenID := uint64(tokenResp.Object().Value("id").Number().Raw())
	token := tokenResp.Object().Value("token").String().Raw()
	testData.UserActivationTokenIDs = append(testData.UserActivationTokenIDs, tokenID)

	// Test token retrieval by ID
	getTokenResp := e.GET(fmt.Sprintf("/accmgt/user-activation-token/%d", tokenID)).
		Expect().
		Status(http.StatusOK).
		JSON()
	getTokenResp.Object().Value("user_id").Number().IsEqual(float64(userID))

	// Test token retrieval by token value
	getByTokenResp := e.GET(fmt.Sprintf("/accmgt/user-activation-token/token/%s", token)).
		Expect().
		Status(http.StatusOK).
		JSON()
	getByTokenResp.Object().Value("user_id").Number().IsEqual(float64(userID))

	// Test token retrieval by user ID
	getByUserResp := e.GET(fmt.Sprintf("/accmgt/user-activation-token/user/%d", userID)).
		Expect().
		Status(http.StatusOK).
		JSON()
	getByUserResp.Object().Value("user_id").Number().IsEqual(float64(userID))

	t.Log("User activation token operations test passed!")
}

// Test State operations (AccState, UserState, AccStateReason)
func TestStateOperations(t *testing.T) {
	defer cleanupTestData()
	e := getExpect(t)

	timestamp := time.Now().Unix()

	// Test AccState operations
	accState := apitypes.IDNameType{
		Name: fmt.Sprintf("TestAccState%d", timestamp),
	}

	accStateResp := e.POST("/accmgt/acc-state").
		WithJSON(accState).
		Expect().
		Status(http.StatusOK).
		JSON()

	accStateID := uint64(accStateResp.Object().Value("id").Number().Raw())
	testData.AccStateIDs = append(testData.AccStateIDs, accStateID)

	// Test AccState retrieval
	e.GET(fmt.Sprintf("/accmgt/acc-state/%d", accStateID)).
		Expect().
		Status(http.StatusOK).
		JSON().Object().Value("name").String().IsEqual(accState.Name)

	// Test AccState list
	e.GET("/accmgt/acc-states").
		WithQuery("page", 0).
		WithQuery("size", 20).
		Expect().
		Status(http.StatusOK).
		JSON().Array()

	// Test UserState operations
	userState := apitypes.IDNameType{
		Name: fmt.Sprintf("TestUserState%d", timestamp),
	}

	userStateResp := e.POST("/accmgt/user-state").
		WithJSON(userState).
		Expect().
		Status(http.StatusOK).
		JSON()

	userStateID := uint64(userStateResp.Object().Value("id").Number().Raw())
	testData.UserStateIDs = append(testData.UserStateIDs, userStateID)

	// Test UserState list
	e.GET("/accmgt/user-states").
		WithQuery("page", 0).
		WithQuery("size", 20).
		Expect().
		Status(http.StatusOK).
		JSON().Array()

	t.Log("State operations test passed!")
}
