package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/accmgtms/model"
)

func (c *Client) UserActivationTokenCreate(ctx context.Context, userID uint64) (*model.UserActivationToken, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtUserActivationToken, userID)

	// Since the API now returns the full UserActivationToken struct,
	// we need to make a POST request and get the response
	// For now, let's use a simple approach - make the POST call and then get the token
	emptyData := struct{}{}
	_, err := c.Client.Client.Create(ctx, url, emptyData, nil)
	if err != nil {
		return nil, err
	}

	// After successful creation, get the token by user ID
	return c.UserActivationTokenGetByUserID(ctx, userID)
}

func (c *Client) UserActivationTokenGetByID(ctx context.Context, id uint64) (*model.UserActivationToken, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtUserActivationToken, id)
	var result model.UserActivationToken
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) UserActivationTokenGetByToken(ctx context.Context, token string) (*model.UserActivationToken, error) {
	url := fmt.Sprintf("%s%s/token/%s", c.UrlPfx, accmgtUserActivationToken, token)
	var result model.UserActivationToken
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) UserActivationTokenGetByUserID(ctx context.Context, userID uint64) (*model.UserActivationToken, error) {
	url := fmt.Sprintf("%s%s/user/%d", c.UrlPfx, accmgtUserActivationToken, userID)
	var result model.UserActivationToken
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) UserActivationTokenUpdate(ctx context.Context, id uint64, data model.UserActivationToken) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtUserActivationToken, id)
	return c.Client.Client.Update(ctx, url, data, nil)
}

func (c *Client) UserActivationTokenDelete(ctx context.Context, id uint64) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtUserActivationToken, id)
	return c.Client.Client.Delete(ctx, url, nil)
}
