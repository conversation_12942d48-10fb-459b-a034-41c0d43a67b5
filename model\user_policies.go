package model

import (
	"time"
)

// UserPolicy represents a link between a user and a policy
type UserPolicy struct {
	UserID    uint64    `json:"user_id" db:"user_id"`
	PolicyID  uint64    `json:"policy_id" db:"policy_id"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// UserPolicyRequest represents the request structure for creating user-policy links
type UserPolicyRequest struct {
	UserID   uint64 `json:"user_id" binding:"required"`
	PolicyID uint64 `json:"policy_id" binding:"required"`
}
