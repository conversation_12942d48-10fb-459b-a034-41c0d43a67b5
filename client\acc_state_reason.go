package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
)

func (c *Client) AccStateReasonCreate(ctx context.Context, data model.AccStateReason) (uint64, error) {
	url := c.UrlPfx + accmgtAccStateReason
	return c.Client.Client.Create(ctx, url, data, nil)
}

func (c *Client) AccStateReasonGetByID(ctx context.Context, id uint64) (*model.AccStateReason, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtAccStateReason, id)
	var result model.AccStateReason
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) AccStateReasonUpdate(ctx context.Context, id uint64, data model.AccStateReason) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtAccStateReason, id)
	return c.Client.Client.Update(ctx, url, data, nil)
}

func (c *Client) AccStateReasonDelete(ctx context.Context, id uint64) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtAccStateReason, id)
	return c.Client.Client.Delete(ctx, url, nil)
}

func (c *Client) AccStateReasonList(ctx context.Context, page apitypes.Page) ([]model.AccStateReason, error) {
	url := c.UrlPfx + accmgtAccStateReasons + "?" + page.AsURLParams()
	var result []model.AccStateReason
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
