package model

// Account state reason constants corresponding to acc_state_reasons table IDs
const (
	// Active state reasons (acc_state_id = 10)
	AccStateReasonActiveEmailValidated = 10 // Email Validated

	// RO state reasons (acc_state_id = 20)
	AccStateReasonRONotActivated = 20 // Not Activated
	AccStateReasonROInitDeletion = 30 // InitDeletion

	// Locked state reasons (acc_state_id = 30)
	AccStateReasonLockedBackend          = 40 // Backend
	AccStateReasonLockedPaymentDefaulted = 50 // PaymentDefaulted

	// Deleted state reasons (acc_state_id = 40)
	AccStateReasonDeletedCustomerRequested = 60 // CustomerRequested
	AccStateReasonDeletedBackend           = 70 // Backend
)
