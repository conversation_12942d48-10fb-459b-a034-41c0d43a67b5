package dao

import (
	"database/sql"

	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/dbutil"
	"github.com/homewizeAI/errs"
)

const (
	selUserState = `SELECT id, name FROM user_states `
)

// scanRowToUserState scans a database row into an IDNameType struct
func scanRowToUserState(scanner dbutil.Scanner) (*apitypes.IDNameType, error) {
	var userState apitypes.IDNameType
	err := scanner.Scan(
		&userState.ID,
		&userState.Name,
	)
	if err != nil {
		return nil, err
	}
	return &userState, nil
}

func UserStateCreate(data apitypes.IDNameType) (uint64, error) {
	query := `
		INSERT INTO user_states (
			name
		) VALUES (
			$1
		)
	`

	id, err := dbutil.ExecGetID(
		query,
		data.Name,
	)

	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, errs.BadInput("relationship")
		}
		if dbutil.ErrIsUniqueViolation(err) {
			return 0, errs.BadInput("name")
		}
		return 0, err
	}

	return id, nil
}

// UserStateGetByID retrieves a user_state by its ID
func UserStateGetByID(id uint64) (*apitypes.IDNameType, error) {
	query := selUserState + ` WHERE id = $1 `

	row := dbutil.QueryRow(query, id)

	userState, err := scanRowToUserState(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("user-state")
		}
		return nil, err
	}

	return userState, nil
}

// UserStateGetByName retrieves a user_state by its name
func UserStateGetByName(name string) (*apitypes.IDNameType, error) {
	query := selUserState + ` WHERE name = $1 `

	row := dbutil.QueryRow(query, name)

	userState, err := scanRowToUserState(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("user-state")
		}
		return nil, err
	}

	return userState, nil
}

// UserStateUpdate updates an existing user_state
func UserStateUpdate(id uint64, data apitypes.IDNameType) (uint64, error) {
	query := `
		UPDATE user_states SET
			name = $1,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = $2
	`

	rowsAffected, err := dbutil.ExecGetAffRows(
		query,
		data.Name,
		id,
	)

	if err != nil {
		if dbutil.ErrIsUniqueViolation(err) {
			return 0, errs.BadInput("name")
		}
		return 0, err
	}

	return rowsAffected, nil
}

// UserStateDelete deletes a user_state by ID
func UserStateDelete(id uint64) error {
	query := `DELETE FROM user_states WHERE id = $1`

	rowsAffected, err := dbutil.ExecGetAffRows(query, id)
	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return errs.BadInput("user-state-in-use")
		}
		return err
	}

	if rowsAffected == 0 {
		return errs.NotFound("user-state")
	}

	return nil
}

// UserStateList retrieves a list of user_states with pagination
func UserStateList(page apitypes.Page) ([]*apitypes.IDNameType, error) {
	query := selUserState + ` ORDER BY name ASC ` + page.AsSQLLimit()

	rows, err := dbutil.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var userStates []*apitypes.IDNameType
	for rows.Next() {
		userState, err := scanRowToUserState(rows)
		if err != nil {
			return nil, err
		}
		userStates = append(userStates, userState)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return userStates, nil
}
