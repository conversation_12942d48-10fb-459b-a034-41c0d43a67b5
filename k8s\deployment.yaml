apiVersion: apps/v1
kind: Deployment
metadata:
  name: accmgtms
  namespace: default
  labels:
    app: accmgtms
spec:
  replicas: 2
  selector:
    matchLabels:
      app: accmgtms
  template:
    metadata:
      labels:
        app: accmgtms
    spec:
      containers:
      - name: accmgtms
        image: github.com/homewizeAI/accmgtms:latest
        ports:
        - containerPort: 3000
        volumeMounts:
        - name: config-volume
          mountPath: /app/conf
          readOnly: true
        env:
        - name: PORT
          value: "3000"
        livenessProbe:
          httpGet:
            path: /accmgt/roles
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /accmgt/roles
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      volumes:
      - name: config-volume
        configMap:
          name: accmgtms-config
