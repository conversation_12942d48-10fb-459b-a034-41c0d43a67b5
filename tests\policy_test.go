package tests

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/homewizeAI/apitypes"
)

func TestPolicyCreate(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)
	timestamp := time.Now().Unix()

	policy := apitypes.IDNameType{
		Name: fmt.Sprintf("Test Policy %d", timestamp),
	}

	resp := e.POST("/accmgt/policy").
		WithJSON(policy).
		Expect().
		Status(http.StatusOK).
		JSON().Object()

	resp.ContainsKey("id")
	id := uint64(resp.Value("id").Number().Raw())
	testData.PolicyIDs = append(testData.PolicyIDs, id)

	t.Logf("Created policy with ID: %d", id)
}

func TestPolicyGetByID(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)
	timestamp := time.Now().Unix()

	// Create test policy
	policy := apitypes.IDNameType{
		Name: fmt.Sprintf("Test Policy GetByID %d", timestamp),
	}

	createResp := e.POST("/accmgt/policy").
		WithJSON(policy).
		Expect().
		Status(http.StatusOK).
		JSON().Object()

	id := uint64(createResp.Value("id").Number().Raw())
	testData.PolicyIDs = append(testData.PolicyIDs, id)

	// Get policy by ID
	resp := e.GET(fmt.Sprintf("/accmgt/policy/%d", id)).
		Expect().
		Status(http.StatusOK).
		JSON().Object()

	resp.Value("id").Number().IsEqual(float64(id))
	resp.Value("name").String().IsEqual(policy.Name)

	t.Logf("Successfully retrieved policy by ID: %d", id)
}

func TestPolicyGetByName(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)
	timestamp := time.Now().Unix()

	// Create test policy
	policy := apitypes.IDNameType{
		Name: fmt.Sprintf("Test Policy GetByName %d", timestamp),
	}

	createResp := e.POST("/accmgt/policy").
		WithJSON(policy).
		Expect().
		Status(http.StatusOK).
		JSON().Object()

	id := uint64(createResp.Value("id").Number().Raw())
	testData.PolicyIDs = append(testData.PolicyIDs, id)

	// Get policy by name
	resp := e.GET(fmt.Sprintf("/accmgt/policy/name/%s", policy.Name)).
		Expect().
		Status(http.StatusOK).
		JSON().Object()

	resp.Value("id").Number().IsEqual(float64(id))
	resp.Value("name").String().IsEqual(policy.Name)

	t.Logf("Successfully retrieved policy by name: %s", policy.Name)
}

func TestPolicyUpdate(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)
	timestamp := time.Now().Unix()

	// Create test policy
	policy := apitypes.IDNameType{
		Name: fmt.Sprintf("Test Policy Update %d", timestamp),
	}

	createResp := e.POST("/accmgt/policy").
		WithJSON(policy).
		Expect().
		Status(http.StatusOK).
		JSON().Object()

	id := uint64(createResp.Value("id").Number().Raw())
	testData.PolicyIDs = append(testData.PolicyIDs, id)

	// Update policy
	updatedPolicy := apitypes.IDNameType{
		Name: fmt.Sprintf("Updated Policy %d", timestamp),
	}

	resp := e.PUT(fmt.Sprintf("/accmgt/policy/%d", id)).
		WithJSON(updatedPolicy).
		Expect().
		Status(http.StatusOK).
		JSON().Object()

	resp.Value("affectedRows").Number().IsEqual(1)

	// Verify update
	getResp := e.GET(fmt.Sprintf("/accmgt/policy/%d", id)).
		Expect().
		Status(http.StatusOK).
		JSON().Object()

	getResp.Value("name").String().IsEqual(updatedPolicy.Name)

	t.Logf("Successfully updated policy ID: %d", id)
}

func TestPolicyDelete(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)
	timestamp := time.Now().Unix()

	// Create test policy
	policy := apitypes.IDNameType{
		Name: fmt.Sprintf("Test Policy Delete %d", timestamp),
	}

	createResp := e.POST("/accmgt/policy").
		WithJSON(policy).
		Expect().
		Status(http.StatusOK).
		JSON().Object()

	id := uint64(createResp.Value("id").Number().Raw())

	// Delete policy
	resp := e.DELETE(fmt.Sprintf("/accmgt/policy/%d", id)).
		Expect().
		Status(http.StatusOK).
		JSON().Object()

	resp.Value("affectedRows").Number().IsEqual(1)

	// Verify deletion
	e.GET(fmt.Sprintf("/accmgt/policy/%d", id)).
		Expect().
		Status(http.StatusNotFound).
		JSON().Object().
		Value("reason").String().IsEqual("policy")

	t.Logf("Successfully deleted policy ID: %d", id)
}

func TestPolicyList(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)
	timestamp := time.Now().Unix()

	// Create multiple test policies
	for i := 0; i < 3; i++ {
		policy := apitypes.IDNameType{
			Name: fmt.Sprintf("Test Policy List %d-%d", timestamp, i),
		}

		createResp := e.POST("/accmgt/policy").
			WithJSON(policy).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		id := uint64(createResp.Value("id").Number().Raw())
		testData.PolicyIDs = append(testData.PolicyIDs, id)
	}

	// List policies
	resp := e.GET("/accmgt/policies").
		WithQuery("page", 0).
		WithQuery("size", 10).
		Expect().
		Status(http.StatusOK).
		JSON().Array()

	resp.Length().IsGreaterOrEqual(3)

	t.Logf("Successfully listed policies")
}

// Test validation and error cases
func TestPolicyValidation(t *testing.T) {
	defer cleanupTestData()

	e := getExpect(t)

	t.Run("CreateWithEmptyName", func(t *testing.T) {
		policy := apitypes.IDNameType{
			Name: "",
		}

		e.POST("/accmgt/policy").
			WithJSON(policy).
			Expect().
			Status(http.StatusBadRequest).
			JSON().Object().
			Value("reason").String().IsEqual("name")
	})

	t.Run("CreateDuplicateName", func(t *testing.T) {
		timestamp := time.Now().Unix()
		policy := apitypes.IDNameType{
			Name: fmt.Sprintf("Duplicate Policy %d", timestamp),
		}

		// Create first policy
		createResp := e.POST("/accmgt/policy").
			WithJSON(policy).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		id := uint64(createResp.Value("id").Number().Raw())
		testData.PolicyIDs = append(testData.PolicyIDs, id)

		// Try to create duplicate
		e.POST("/accmgt/policy").
			WithJSON(policy).
			Expect().
			Status(http.StatusBadRequest).
			JSON().Object().
			Value("reason").String().IsEqual("name")
	})

	t.Run("GetNonExistentPolicy", func(t *testing.T) {
		e.GET("/accmgt/policy/99999").
			Expect().
			Status(http.StatusNotFound).
			JSON().Object().
			Value("reason").String().IsEqual("policy")
	})

	t.Run("GetByInvalidID", func(t *testing.T) {
		e.GET("/accmgt/policy/invalid").
			Expect().
			Status(http.StatusBadRequest).
			JSON().Object().
			Value("reason").String().IsEqual("id")
	})

	t.Run("UpdateNonExistentPolicy", func(t *testing.T) {
		policy := apitypes.IDNameType{
			Name: "Non-existent Policy",
		}

		resp := e.PUT("/accmgt/policy/99999").
			WithJSON(policy).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		resp.Value("affectedRows").Number().IsEqual(0)
	})

	t.Run("DeleteNonExistentPolicy", func(t *testing.T) {
		resp := e.DELETE("/accmgt/policy/99999").
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		resp.Value("affectedRows").Number().IsEqual(0)
	})

	t.Logf("All policy validation tests passed!")
}
