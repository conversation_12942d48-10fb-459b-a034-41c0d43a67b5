package client

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
	"github.com/jarcoal/httpmock"
)

func TestRoleCreate(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"POST",
		"http://localhost:3000/accmgt/role",
		func(req *http.Request) (*http.Response, error) {
			var role model.Role
			err := json.NewDecoder(req.Body).Decode(&role)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			var resp apitypes.IDType
			resp.ID = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	var role model.Role
	role.Name = "Admin"
	id, err := c.<PERSON>(context.Background(), role)
	if err != nil {
		t.Error(err)
	}
	if id != 1 {
		t.<PERSON><PERSON><PERSON>("Expected id 1, got %d", id)
	}
}

func TestRoleGetByID(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/role/1",
		func(req *http.Request) (*http.Response, error) {
			role := model.Role{
				ID:   1,
				Name: "Admin",
			}
			return httpmock.NewJsonResponse(http.StatusOK, role)
		},
	)

	role, err := c.RoleGetByID(context.Background(), 1)
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if role == nil {
		t.Error("Expected role response, got nil")
	}
}

func TestRoleGetByName(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/role/Admin",
		func(req *http.Request) (*http.Response, error) {
			role := model.Role{
				ID:   1,
				Name: "Admin",
			}
			return httpmock.NewJsonResponse(http.StatusOK, role)
		},
	)

	role, err := c.RoleGetByName(context.Background(), "Admin")
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if role == nil {
		t.Error("Expected role response, got nil")
	}
}

func TestRoleUpdate(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"PUT",
		"http://localhost:3000/accmgt/role/1",
		func(req *http.Request) (*http.Response, error) {
			var role model.Role
			err := json.NewDecoder(req.Body).Decode(&role)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			var resp apitypes.AffectedRowsType
			resp.AffectedRows = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	var role model.Role
	role.Name = "Super Admin"
	rowsAffected, err := c.RoleUpdate(context.Background(), 1, role)
	if err != nil {
		t.Error(err)
	}
	if rowsAffected != 1 {
		t.Errorf("Expected 1 row affected, got %d", rowsAffected)
	}
}

func TestRoleDelete(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"DELETE",
		"http://localhost:3000/accmgt/role/1",
		func(req *http.Request) (*http.Response, error) {
			var resp apitypes.AffectedRowsType
			resp.AffectedRows = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	rowsAffected, err := c.RoleDelete(context.Background(), 1)
	if err != nil {
		t.Error(err)
	}
	if rowsAffected != 1 {
		t.Errorf("Expected 1 row affected, got %d", rowsAffected)
	}
}

func TestRoleList(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/roles",
		func(req *http.Request) (*http.Response, error) {
			roles := []model.Role{
				{ID: 1, Name: "Admin"},
				{ID: 2, Name: "User"},
			}
			return httpmock.NewJsonResponse(http.StatusOK, roles)
		},
	)

	page := apitypes.Page{Page: 0, Size: 20}
	roles, err := c.RoleList(context.Background(), page)
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if roles == nil {
		t.Error("Expected roles response, got nil")
	}
}
