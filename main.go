package main

import (
	"fmt"
	"log"

	"github.com/homewizeAI/accmgtms/api"
	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apicommongin"
	"github.com/homewizeAI/dbutil"
)

func main() {
	// Initialize database
	dbutil.Init()
	defer dbutil.Close()

	// Create Gin router
	r := apicommongin.NewRouter()

	// Account routes
	g := r.Group("/accmgt")

	g.POST("/account", api.AccCreate)                         // POST /accounts
	g.GET("/account/:id", api.AccGetByID)                     // GET /accounts/:id
	g.GET("/account/short/:shortName", api.AccGetByShortName) // GET /accounts/short/:shortName
	g.PUT("/account/:id", api.AccUpdate)                      // PUT /accounts/:id
	g.DELETE("/account/:id", api.AccDelete)                   // DELETE /accounts/:id
	g.GET("/accounts", api.AccList)

	g.POST("/role", api.RoleCreate)              // POST /roles
	g.GET("/role/:id", api.RoleGetByID)          // GET /roles/:id
	g.GET("/role/name/:name", api.RoleGetByName) // GET /roles/name/:name
	g.PUT("/role/:id", api.RoleUpdate)           // PUT /roles/:id
	g.DELETE("/role/:id", api.RoleDelete)        // DELETE /roles/:id
	g.GET("/roles", api.RoleList)                // GET /roles?page=0&size=20

	g.POST("/user", api.UserCreate)                 // POST /users
	g.GET("/user/:id", api.UserGetByID)             // GET /users/:id
	g.GET("/user/email/:email", api.UserGetByEmail) // GET /users/email/:email
	g.GET("/user/by", api.UsrBy)                    // GET /user/by?key=123&token=abc OR ?email=<EMAIL>&shortName=company OR ?shortName=company
	g.PUT("/user/:id", api.UserUpdate)              // PUT /users/:id
	g.DELETE("/user/:id", api.UserDelete)           // DELETE /users/:id
	g.GET("/users", api.UserList)                   // GET /users?page=0&size=20

	g.POST("/acc-state", api.AccStateCreate)              // POST /acc-states
	g.GET("/acc-state/:id", api.AccStateGetByID)          // GET /acc-states/:id
	g.GET("/acc-state/name/:name", api.AccStateGetByName) // GET /acc-states/name/:name
	g.PUT("/acc-state/:id", api.AccStateUpdate)           // PUT /acc-states/:id
	g.DELETE("/acc-state/:id", api.AccStateDelete)        // DELETE /acc-states/:id
	g.GET("/acc-states", api.AccStateList)                // GET /acc-states?page=0&size=20

	g.POST("/acc-blacklist", api.AccBlacklistCreate)              // POST /acc-blacklists
	g.GET("/acc-blacklist/:id", api.AccBlacklistGetByID)          // GET /acc-blacklists/:id
	g.GET("/acc-blacklist/name/:name", api.AccBlacklistGetByName) // GET /acc-blacklists/name/:name
	g.PUT("/acc-blacklist/:id", api.AccBlacklistUpdate)           // PUT /acc-blacklists/:id
	g.DELETE("/acc-blacklist/:id", api.AccBlacklistDelete)        // DELETE /acc-blacklists/:id
	g.GET("/acc-blacklists", api.AccBlacklistList)                // GET /acc-blacklists?page=0&size=20

	g.POST("/policy", api.PolicyCreate)              // POST /policies
	g.GET("/policy/:id", api.PolicyGetByID)          // GET /policies/:id
	g.GET("/policy/name/:name", api.PolicyGetByName) // GET /policies/name/:name
	g.PUT("/policy/:id", api.PolicyUpdate)           // PUT /policies/:id
	g.DELETE("/policy/:id", api.PolicyDelete)        // DELETE /policies/:id
	g.GET("/policies", api.PolicyList)               // GET /policies?page=0&size=20

	g.POST("/permission", api.PermissionCreate)              // POST /permissions
	g.GET("/permission/:id", api.PermissionGetByID)          // GET /permissions/:id
	g.GET("/permission/name/:name", api.PermissionGetByName) // GET /permissions/name/:name
	g.PUT("/permission/:id", api.PermissionUpdate)           // PUT /permissions/:id
	g.DELETE("/permission/:id", api.PermissionDelete)        // DELETE /permissions/:id
	g.GET("/permissions", api.PermissionList)                // GET /permissions?page=0&size=20

	g.POST("/acc-state-reason", api.AccStateReasonCreate)       // POST /acc-state-reasons
	g.GET("/acc-state-reason/:id", api.AccStateReasonGetByID)   // GET /acc-state-reasons/:id
	g.PUT("/acc-state-reason/:id", api.AccStateReasonUpdate)    // PUT /acc-state-reasons/:id
	g.DELETE("/acc-state-reason/:id", api.AccStateReasonDelete) // DELETE /acc-state-reasons/:id
	g.GET("/acc-state-reasons", api.AccStateReasonList)         // GET /acc-state-reasons?page=0&size=20

	g.POST("/user-activation-token/:userID", api.UserActivationTokenCreate)          // POST /user-activation-tokens/:userID
	g.GET("/user-activation-token/:id", api.UserActivationTokenGetByID)              // GET /user-activation-tokens/:id
	g.GET("/user-activation-token/token/:token", api.UserActivationTokenGetByToken)  // GET /user-activation-tokens/token/:token
	g.GET("/user-activation-token/user/:userID", api.UserActivationTokenGetByUserID) // GET /user-activation-tokens/user/:userID
	g.PUT("/user-activation-token/:id", api.UserActivationTokenUpdate)               // PUT /user-activation-tokens/:id
	g.DELETE("/user-activation-token/:id", api.UserActivationTokenDelete)            // DELETE /user-activation-tokens/:id

	g.POST("/user-state", api.UserStateCreate)              // POST /user-states
	g.GET("/user-state/:id", api.UserStateGetByID)          // GET /user-states/:id
	g.GET("/user-state/name/:name", api.UserStateGetByName) // GET /user-states/name/:name
	g.PUT("/user-state/:id", api.UserStateUpdate)           // PUT /user-states/:id
	g.DELETE("/user-state/:id", api.UserStateDelete)        // DELETE /user-states/:id
	g.GET("/user-states", api.UserStateList)                // GET /user-states?page=0&size=20

	g.POST("/signup", api.Signup) // POST /signup

	// Activation route
	g.PATCH("/account/activate", api.AccountActivate) // PATCH /activate

	// Additional utility routes
	g.GET("/accounts/count", api.AccCount)                                       // GET /accounts/count
	g.GET("/account/check-availability/:shortName", api.AccShortNameIsAvailable) // GET /account/check-availability/:shortName

	g.GET("/user/is-active/:id", api.UserIsActive)

	// Start server
	port := apicommon.CliPort(3000) // Default port is 3000
	portStr := fmt.Sprintf(":%d", port)
	log.Printf("Starting server on %s", portStr)
	if err := r.Run(portStr); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
