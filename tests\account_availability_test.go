package tests

import (
	"fmt"
	"net/http"
	"testing"
	"time"
)

func TestAccCheckShortNameAvailable(t *testing.T) {
	defer cleanupTestData()
	e := getExpect(t)

	// Test with available name
	availableName := fmt.Sprintf("available%d", time.Now().Unix())

	availableResp := e.GET(fmt.Sprintf("/accmgt/account/check-availability/%s", availableName)).
		Expect().
		Status(http.StatusOK).
		JSON()

	availableResp.Object().Value("bool").Boolean().IsTrue()

	// Create an account to test unavailable name
	account := createTestAccount()
	resp := e.POST("/accmgt/account").
		WithJSON(account).
		Expect().
		Status(http.StatusOK).
		JSON()

	accountID := uint64(resp.Object().Value("id").Number().Raw())
	testData.AccountIDs = append(testData.AccountIDs, accountID)

	// Test with unavailable name (existing account)
	unavailableResp := e.GET(fmt.Sprintf("/accmgt/account/check-availability/%s", account.NameShort)).
		Expect().
		Status(http.StatusOK).
		JSON()

	unavailableResp.Object().Value("bool").Boolean().IsFalse()

	t.Log("Account availability check test passed!")
}

func TestAccCheckShortNameAvailableValidation(t *testing.T) {
	defer cleanupTestData()
	e := getExpect(t)

	// Test with empty name
	e.GET("/accmgt/account/check-availability/").
		Expect().
		Status(http.StatusBadRequest) // Route won't match properly

	// Test with too short name
	e.GET("/accmgt/account/check-availability/a").
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("short-name-length")

	// Test with too long name (over 50 characters)
	longName := "this-is-a-very-long-name-that-exceeds-fifty-characters-limit"
	e.GET(fmt.Sprintf("/accmgt/account/check-availability/%s", longName)).
		Expect().
		Status(http.StatusBadRequest).
		JSON().Object().Value("reason").String().IsEqual("short-name-length")

	t.Log("Account availability validation test passed!")
}
