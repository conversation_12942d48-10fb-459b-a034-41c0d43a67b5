package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/apitypes"
)

func (c *Client) AccBlacklistCreate(ctx context.Context, data apitypes.IDNameType) (uint64, error) {
	url := c.UrlPfx + accmgtAccBlacklist
	return c.Client.Client.Create(ctx, url, data, nil)
}

func (c *Client) AccBlacklistGetByID(ctx context.Context, id uint64) (*apitypes.IDNameType, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtAccBlacklist, id)
	var result apitypes.IDNameType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) AccBlacklistGetByName(ctx context.Context, name string) (*apitypes.IDNameType, error) {
	url := fmt.Sprintf("%s%s/name/%s", c.UrlPfx, accmgtAccBlacklist, name)
	var result apitypes.IDNameType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) AccBlacklistUpdate(ctx context.Context, id uint64, data apitypes.IDNameType) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtAccBlacklist, id)
	return c.Client.Client.Update(ctx, url, data, nil)
}

func (c *Client) AccBlacklistDelete(ctx context.Context, id uint64) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtAccBlacklist, id)
	return c.Client.Client.Delete(ctx, url, nil)
}

func (c *Client) AccBlacklistList(ctx context.Context, page apitypes.Page) ([]apitypes.IDNameType, error) {
	url := c.UrlPfx + accmgtAccBlacklists + "?" + page.AsURLParams()
	var result []apitypes.IDNameType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
