package api

import (
	"github.com/gin-gonic/gin"

	"github.com/homewizeAI/accmgtms/dao"
	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/errs"
	"github.com/homewizeAI/utils"
)

func RoleCreate(c *gin.Context) {
	var err error
	var data model.Role
	if err = c.BindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	id, err := dao.RoleCreate(data)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		if errs.IsConflict(err) {
			apicommon.RespConflict(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err.<PERSON>rror())
		return
	}
	apicommon.RespOk(c, apitypes.IDTypeNew(id))
}

func RoleGetByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	role, err := dao.RoleGetByID(id)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, role)
}

func RoleGetByName(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		apicommon.RespBadRequest(c, "name")
		return
	}

	role, err := dao.RoleGetByName(name)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, role)
}

func RoleUpdate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	var data model.Role
	if err = c.BindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	rowsAffected, err := dao.RoleUpdate(id, data)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(rowsAffected))
}

func RoleDelete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	rowsAffected, err := dao.RoleDelete(id)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(rowsAffected))
}

func RoleList(c *gin.Context) {
	page := apicommon.GetPage(c)
	roles, err := dao.RoleList(page)
	if err != nil {
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, roles)
}
