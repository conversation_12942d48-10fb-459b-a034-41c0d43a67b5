package client

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/jarcoal/httpmock"
)

func TestSignup(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"POST",
		"http://localhost:3000/accmgt/signup",
		func(req *http.Request) (*http.Response, error) {
			var data model.SignupRequest
			err := json.NewDecoder(req.Body).Decode(&data)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}

			response := model.SignupResponse{
				AccID:           1,
				UserID:          1,
				ActivationKeyID: 1,
				ActivationToken: "test-token-123",
			}
			return httpmock.NewJsonResponse(http.StatusOK, response)
		},
	)

	var account model.Account
	account.NameRegistered = "Test Company"
	account.NameShort = "testco"

	var user model.User
	user.UserStateID = 1 // Will be set to default in signup if 0
	user.NameFirst = "John"
	user.NameLast = "Doe"
	user.Email = "<EMAIL>"

	request := model.SignupRequest{
		Account: account,
		User:    user,
	}

	response, err := c.Signup(context.Background(), request)
	if err != nil {
		t.Error(err)
	}
	// Verify we got a proper response with expected values
	if response == nil {
		t.Error("Expected signup response, got nil")
	}
	if response.AccID != 1 {
		t.Errorf("Expected AccID 1, got %d", response.AccID)
	}
	if response.UserID != 1 {
		t.Errorf("Expected UserID 1, got %d", response.UserID)
	}
	if response.ActivationKeyID != 1 {
		t.Errorf("Expected ActivationKeyID 1, got %d", response.ActivationKeyID)
	}
	if response.ActivationToken != "test-token-123" {
		t.Errorf("Expected ActivationToken 'test-token-123', got '%s'", response.ActivationToken)
	}
}
