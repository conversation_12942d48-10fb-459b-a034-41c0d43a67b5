package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/apitypes"
)

func (c *Client) UserStateCreate(ctx context.Context, data apitypes.IDNameType) (uint64, error) {
	url := c.UrlPfx + accmgtUserState
	return c.Client.Client.Create(ctx, url, data, nil)
}

func (c *Client) UserStateGetByID(ctx context.Context, id uint64) (*apitypes.IDNameType, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtUserState, id)
	var result apitypes.IDNameType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) UserStateGetByName(ctx context.Context, name string) (*apitypes.IDNameType, error) {
	url := fmt.Sprintf("%s%s/name/%s", c.UrlPfx, accmgtUserState, name)
	var result apitypes.IDNameType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) UserStateUpdate(ctx context.Context, id uint64, data apitypes.IDNameType) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtUserState, id)
	return c.Client.Client.Update(ctx, url, data, nil)
}

func (c *Client) UserStateDelete(ctx context.Context, id uint64) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtUserState, id)
	return c.Client.Client.Delete(ctx, url, nil)
}

func (c *Client) UserStateList(ctx context.Context, page apitypes.Page) ([]apitypes.IDNameType, error) {
	url := c.UrlPfx + accmgtUserStates + "?" + page.AsURLParams()
	var result []apitypes.IDNameType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
