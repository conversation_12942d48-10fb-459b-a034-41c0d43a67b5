package dao

import (
	"database/sql"

	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/dbutil"
	"github.com/homewizeAI/errs"
)

const (
	selPermission = `SELECT id, name FROM permissions `
)

func PermissionCreate(data apitypes.IDNameType) (uint64, error) {
	query := `
		INSERT INTO permissions (
			name
		) VALUES (
			$1
		) RETURNING id
	`

	var id uint64
	err := dbutil.QueryRow(query, data.Name).Scan(&id)

	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, errs.BadInput("relationship")
		}
		if dbutil.ErrIsUniqueViolation(err) {
			return 0, errs.BadInput("name")
		}
		return 0, err
	}

	return id, nil
}

func PermissionGetByID(id uint64) (*apitypes.IDNameType, error) {
	query := selPermission + `WHERE id = $1`
	row := dbutil.QueryRow(query, id)
	permission, err := scanRowToPermission(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("permission")
		}
		return nil, err
	}
	return permission, nil
}

func PermissionGetByName(name string) (*apitypes.IDNameType, error) {
	query := selPermission + `WHERE name = $1`
	row := dbutil.QueryRow(query, name)
	permission, err := scanRowToPermission(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("permission")
		}
		return nil, err
	}
	return permission, nil
}

func PermissionUpdate(id uint64, data apitypes.IDNameType) (uint64, error) {
	query := `UPDATE permissions SET name = $1 WHERE id = $2`
	affectedRows, err := dbutil.ExecGetAffRows(query, data.Name, id)
	if err != nil {
		if dbutil.ErrIsUniqueViolation(err) {
			return 0, errs.BadInput("name")
		}
		return 0, err
	}
	return affectedRows, nil
}

func PermissionDelete(id uint64) (uint64, error) {
	query := `DELETE FROM permissions WHERE id = $1`
	affectedRows, err := dbutil.ExecGetAffRows(query, id)
	if err != nil {
		return 0, err
	}
	return affectedRows, nil
}

func PermissionList(page apitypes.Page) ([]apitypes.IDNameType, error) {
	query := selPermission + `ORDER BY name ` + page.AsSQLLimit()
	rows, err := dbutil.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var permissions []apitypes.IDNameType
	for rows.Next() {
		permission, err := scanRowToPermission(rows)
		if err != nil {
			return nil, err
		}
		permissions = append(permissions, *permission)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return permissions, nil
}

// scanRowToPermission scans a database row into an IDNameType struct
func scanRowToPermission(row interface{ Scan(...interface{}) error }) (*apitypes.IDNameType, error) {
	var permission apitypes.IDNameType
	err := row.Scan(&permission.ID, &permission.Name)
	return &permission, err
}
