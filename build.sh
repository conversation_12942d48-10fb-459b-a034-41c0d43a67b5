#!/bin/bash

# Simple build script for Account Management Service

set -e

echo "🔨 Building Account Management Service..."

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go first."
    exit 1
fi

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -f accmgtms

# Download dependencies
echo "📦 Downloading dependencies..."
go mod tidy

# Build the application
echo "🔨 Building application..."
go build -o accmgtms main.go

echo "✅ Build successful!"
echo "🚀 Run with: ./accmgtms"
