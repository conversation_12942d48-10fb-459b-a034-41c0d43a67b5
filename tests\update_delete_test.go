package tests

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/homewizeAI/accmgtms/model"
)

// Test Update and Delete operations for all entities
func TestUpdateDeleteOperations(t *testing.T) {
	defer cleanupTestData()
	e := getExpect(t)

	timestamp := time.Now().UnixNano()

	// Test Account Update and Delete
	t.Run("AccountUpdateDelete", func(t *testing.T) {
		// Create account first
		account := model.Account{
			NameRegistered: fmt.Sprintf("Update Test Company %d", timestamp),
			NameShort:      fmt.Sprintf("updateco%d", timestamp),
			PhoneNumber:    "555-1111",
		}

		resp := e.POST("/accmgt/account").
			WithJSON(account).
			Expect().
			Status(http.StatusOK).
			JSON()

		accountID := uint64(resp.Object().Value("id").Number().Raw())
		testData.AccountIDs = append(testData.AccountIDs, accountID)

		// Update account
		updatedAccount := model.Account{
			NameRegistered: fmt.Sprintf("Updated Company %d", timestamp),
			NameShort:      fmt.Sprintf("updatedco%d", timestamp),
			PhoneNumber:    "555-2222",
		}

		e.PUT(fmt.Sprintf("/accmgt/account/%d", accountID)).
			WithJSON(updatedAccount).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("affected_rows").Number().Ge(0)

		// Verify update
		e.GET(fmt.Sprintf("/accmgt/account/%d", accountID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("name_registered").String().IsEqual(updatedAccount.NameRegistered)

		// Delete account
		e.DELETE(fmt.Sprintf("/accmgt/account/%d", accountID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("affected_rows").Number().Ge(0)

		// Verify deletion
		e.GET(fmt.Sprintf("/accmgt/account/%d", accountID)).
			Expect().
			Status(http.StatusNotFound)

		t.Log("Account update/delete operations passed!")
	})

	// Test Role Update and Delete
	t.Run("RoleUpdateDelete", func(t *testing.T) {
		// Create role first
		role := model.Role{
			Name: fmt.Sprintf("UpdateTestRole%d", timestamp),
		}

		resp := e.POST("/accmgt/role").
			WithJSON(role).
			Expect().
			Status(http.StatusOK).
			JSON()

		roleID := uint64(resp.Object().Value("id").Number().Raw())
		testData.RoleIDs = append(testData.RoleIDs, roleID)

		// Update role
		updatedRole := model.Role{
			Name: fmt.Sprintf("UpdatedRole%d", timestamp),
		}

		e.PUT(fmt.Sprintf("/accmgt/role/%d", roleID)).
			WithJSON(updatedRole).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("affected_rows").Number().Ge(0)

		// Verify update
		e.GET(fmt.Sprintf("/accmgt/role/%d", roleID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("name").String().IsEqual(updatedRole.Name)

		// Delete role
		e.DELETE(fmt.Sprintf("/accmgt/role/%d", roleID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("affected_rows").Number().Ge(0)

		// Verify deletion
		e.GET(fmt.Sprintf("/accmgt/role/%d", roleID)).
			Expect().
			Status(http.StatusNotFound)

		t.Log("Role update/delete operations passed!")
	})

	// Test User Update and Delete
	t.Run("UserUpdateDelete", func(t *testing.T) {
		// Create account first
		account := model.Account{
			NameRegistered: fmt.Sprintf("User Update Test Company %d", timestamp),
			NameShort:      fmt.Sprintf("userupdco%d", timestamp),
			PhoneNumber:    "555-3333",
		}

		accResp := e.POST("/accmgt/account").
			WithJSON(account).
			Expect().
			Status(http.StatusOK).
			JSON()

		accountID := uint64(accResp.Object().Value("id").Number().Raw())
		testData.AccountIDs = append(testData.AccountIDs, accountID)

		// Create user
		user := model.User{
			AccID:       accountID,
			RoleID:      10, // Admin role from seed data
			UserStateID: 10, // Not Validated state from seed data
			NameFirst:   "Update",
			NameLast:    "User",
			Email:       fmt.Sprintf("<EMAIL>", timestamp),
		}

		userResp := e.POST("/accmgt/user").
			WithJSON(user).
			Expect().
			Status(http.StatusOK).
			JSON()

		userID := uint64(userResp.Object().Value("id").Number().Raw())
		testData.UserIDs = append(testData.UserIDs, userID)

		// Update user
		updatedUser := model.User{
			AccID:       accountID,
			RoleID:      10,
			UserStateID: 10,
			NameFirst:   "Updated",
			NameLast:    "TestUser",
			Email:       fmt.Sprintf("<EMAIL>", timestamp),
		}

		e.PUT(fmt.Sprintf("/accmgt/user/%d", userID)).
			WithJSON(updatedUser).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("affected_rows").Number().Ge(0)

		// Verify update
		e.GET(fmt.Sprintf("/accmgt/user/%d", userID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("name_first").String().IsEqual(updatedUser.NameFirst)

		// Delete user
		e.DELETE(fmt.Sprintf("/accmgt/user/%d", userID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("affected_rows").Number().Ge(0)

		// Verify deletion
		e.GET(fmt.Sprintf("/accmgt/user/%d", userID)).
			Expect().
			Status(http.StatusNotFound)

		t.Log("User update/delete operations passed!")
	})
}
