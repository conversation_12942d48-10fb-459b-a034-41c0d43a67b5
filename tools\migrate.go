package main

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"

	_ "github.com/lib/pq"
)

const (
	dbHost     = "localhost"
	dbPort     = 5432
	dbUser     = "postgres"
	dbPassword = "postgres"
	dbName     = "accmgtms"
)

type Migration struct {
	Version int
	File    string
}

var upMigrations = []Migration{
	{1, "db/migrations/001_create_acc_states_up.sql"},
	{2, "db/migrations/002_insert_acc_states_up.sql"},
	{3, "db/migrations/003_create_acc_state_reason_up.sql"},
	{4, "db/migrations/004_insert_acc_state_reasons_up.sql"},
	{5, "db/migrations/005_create_accounts_up.sql"},
	{6, "db/migrations/006_create_roles_up.sql"},
	{7, "db/migrations/007_insert_roles_up.sql"},
	{8, "db/migrations/008_create_user_states_up.sql"},
	{9, "db/migrations/009_insert_user_states_up.sql"},
	{10, "db/migrations/010_create_users_up.sql"},
	{11, "db/migrations/011_create_user_activation_token_up.sql"},
	{12, "db/migrations/012_create_acc_blacklists_up.sql"},
	{13, "db/migrations/013_insert_acc_blacklists_up.sql"},
}

var downMigrations = []Migration{
	{13, "db/migrations/013_insert_acc_blacklists_down.sql"},
	{12, "db/migrations/012_create_acc_blacklists_down.sql"},
	{11, "db/migrations/011_create_user_activation_token_down.sql"},
	{10, "db/migrations/010_create_users_down.sql"},
	{9, "db/migrations/009_insert_user_states_down.sql"},
	{8, "db/migrations/008_create_user_states_down.sql"},
	{7, "db/migrations/007_insert_roles_down.sql"},
	{6, "db/migrations/006_create_roles_down.sql"},
	{5, "db/migrations/005_create_accounts_down.sql"},
	{4, "db/migrations/004_insert_acc_state_reasons_down.sql"},
	{3, "db/migrations/003_create_acc_state_reason_down.sql"},
	{2, "db/migrations/002_insert_acc_states_down.sql"},
	{1, "db/migrations/001_create_acc_states_down.sql"},
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run migrate.go [up|down]")
		os.Exit(1)
	}

	command := os.Args[1]
	if command != "up" && command != "down" {
		fmt.Println("Usage: go run migrate.go [up|down]")
		os.Exit(1)
	}

	// Connect to database
	psqlInfo := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	db, err := sql.Open("postgres", psqlInfo)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	err = db.Ping()
	if err != nil {
		log.Fatal("Failed to ping database:", err)
	}

	fmt.Println("Connected to database successfully")

	// Create schema_migrations table if it doesn't exist
	createTableSQL := `
	CREATE TABLE IF NOT EXISTS schema_migrations (
		version BIGINT PRIMARY KEY,
		dirty BOOLEAN NOT NULL DEFAULT FALSE
	);`

	_, err = db.Exec(createTableSQL)
	if err != nil {
		log.Fatal("Failed to create schema_migrations table:", err)
	}

	if command == "up" {
		runUpMigrations(db)
	} else {
		runDownMigrations(db)
	}
}

func isMigrationApplied(db *sql.DB, version int) bool {
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM schema_migrations WHERE version = $1", version).Scan(&count)
	if err != nil {
		log.Printf("Error checking migration status: %v", err)
		return false
	}
	return count > 0
}

func markMigrationApplied(db *sql.DB, version int) error {
	_, err := db.Exec("INSERT INTO schema_migrations (version, dirty) VALUES ($1, FALSE)", version)
	return err
}

func markMigrationUnapplied(db *sql.DB, version int) error {
	_, err := db.Exec("DELETE FROM schema_migrations WHERE version = $1", version)
	return err
}

func executeMigrationFile(db *sql.DB, filename string) error {
	content, err := ioutil.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("failed to read file %s: %v", filename, err)
	}

	// Split by semicolon and execute each statement
	statements := strings.Split(string(content), ";")
	for _, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" {
			continue
		}
		_, err = db.Exec(stmt)
		if err != nil {
			return fmt.Errorf("failed to execute statement: %v", err)
		}
	}
	return nil
}

func runUpMigrations(db *sql.DB) {
	fmt.Println("Starting UP migrations...")

	for _, migration := range upMigrations {
		if isMigrationApplied(db, migration.Version) {
			fmt.Printf("Migration %d already applied, skipping...\n", migration.Version)
			continue
		}

		fmt.Printf("Applying migration %d: %s\n", migration.Version, migration.File)

		err := executeMigrationFile(db, migration.File)
		if err != nil {
			log.Fatalf("Failed to apply migration %d: %v", migration.Version, err)
		}

		err = markMigrationApplied(db, migration.Version)
		if err != nil {
			log.Fatalf("Failed to mark migration %d as applied: %v", migration.Version, err)
		}

		fmt.Printf("Migration %d applied successfully\n", migration.Version)
	}

	fmt.Println("All UP migrations completed successfully!")
}

func runDownMigrations(db *sql.DB) {
	fmt.Println("Starting DOWN migrations...")

	for _, migration := range downMigrations {
		if !isMigrationApplied(db, migration.Version) {
			fmt.Printf("Migration %d not applied, skipping...\n", migration.Version)
			continue
		}

		fmt.Printf("Rolling back migration %d: %s\n", migration.Version, migration.File)

		err := executeMigrationFile(db, migration.File)
		if err != nil {
			log.Fatalf("Failed to rollback migration %d: %v", migration.Version, err)
		}

		err = markMigrationUnapplied(db, migration.Version)
		if err != nil {
			log.Fatalf("Failed to mark migration %d as unapplied: %v", migration.Version, err)
		}

		fmt.Printf("Migration %d rolled back successfully\n", migration.Version)
	}

	fmt.Println("All DOWN migrations completed successfully!")
}
