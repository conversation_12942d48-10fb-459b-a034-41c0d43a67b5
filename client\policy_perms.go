package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
)

func (c *Client) PolicyPermCreate(ctx context.Context, data model.PolicyPermRequest) error {
	url := c.UrlPfx + accmgtPolicyPerm
	_, err := c.Client.Client.Create(ctx, url, data, nil)
	return err
}

func (c *Client) PolicyPermGetByPolicyID(ctx context.Context, policyID uint64) ([]model.PolicyPerm, error) {
	url := fmt.Sprintf("%s%s/policy/%d", c.UrlPfx, accmgtPolicyPerm, policyID)
	var result []model.PolicyPerm
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) PolicyPermGetByPermissionID(ctx context.Context, permissionID uint64) ([]model.PolicyPerm, error) {
	url := fmt.Sprintf("%s%s/permission/%d", c.UrlPfx, accmgtPolicyPerm, permissionID)
	var result []model.PolicyPerm
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) PolicyPermDelete(ctx context.Context, policyID, permissionID uint64) (uint64, error) {
	url := fmt.Sprintf("%s%s/policy/%d/permission/%d", c.UrlPfx, accmgtPolicyPerm, policyID, permissionID)
	return c.Client.Client.Delete(ctx, url, nil)
}

func (c *Client) PolicyPermList(ctx context.Context, page apitypes.Page) ([]model.PolicyPerm, error) {
	url := c.UrlPfx + accmgtPolicyPerms + "?" + page.AsURLParams()
	var result []model.PolicyPerm
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
