package model

import (
	"time"
)

// Account represents an account in the system
type Account struct {
	ID               uint64    `json:"id" db:"id"`
	AccStateID       *uint64   `json:"acc_state_id" db:"acc_state_id"`
	AccStateReasonID *uint64   `json:"acc_state_reason_id" db:"acc_state_reason_id"`
	NameRegistered   string    `json:"name_registered" db:"name_registered"`
	NameShort        string    `json:"name_short" db:"name_short"`
	AddrLine1        *string   `json:"addr_line1" db:"addr_line1"`
	AddrLine2        *string   `json:"addr_line2" db:"addr_line2"`
	AddrCity         *string   `json:"addr_city" db:"addr_city"`
	AddrState        *string   `json:"addr_state" db:"addr_state"`
	AddrCountry      *string   `json:"addr_country" db:"addr_country"`
	AddrPostalCode   *string   `json:"addr_postal_code" db:"addr_postal_code"`
	PhoneCC          *string   `json:"phone_cc" db:"phone_cc"`
	PhoneNumber      string    `json:"phone_number" db:"phone_number"`
	CreatedAt        time.Time `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time `json:"updated_at" db:"updated_at"`
}
