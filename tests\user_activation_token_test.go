package tests

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/homewizeAI/accmgtms/model"
)

// Test UserActivationToken CRUD operations
func TestUserActivationTokenCRUDOperations(t *testing.T) {
	defer cleanupTestData()
	e := getExpect(t)

	timestamp := time.Now().Unix()

	t.Run("UserActivationTokenCRUD", func(t *testing.T) {
		// First create an account and user for the token
		account := model.Account{
			NameRegistered: fmt.Sprintf("Token Test Company %d", timestamp),
			NameShort:      fmt.Sprintf("tokenco%d", timestamp),
			PhoneNumber:    "555-4444",
		}

		accResp := e.POST("/accmgt/account").
			WithJSON(account).
			Expect().
			Status(http.StatusOK).
			JSON()

		accountID := uint64(accResp.Object().Value("id").Number().Raw())
		testData.AccountIDs = append(testData.AccountIDs, accountID)

		// Create user
		user := model.User{
			AccID:       accountID,
			RoleID:      10, // Admin role from seed data
			UserStateID: 10, // Not Validated state from seed data
			NameFirst:   "Token",
			NameLast:    "User",
			Email:       fmt.Sprintf("<EMAIL>", timestamp),
		}

		userResp := e.POST("/accmgt/user").
			WithJSON(user).
			Expect().
			Status(http.StatusOK).
			JSON()

		userID := uint64(userResp.Object().Value("id").Number().Raw())
		testData.UserIDs = append(testData.UserIDs, userID)

		// Create UserActivationToken
		tokenResp := e.POST(fmt.Sprintf("/accmgt/user-activation-token/%d", userID)).
			WithJSON(struct{}{}).
			Expect().
			Status(http.StatusOK).
			JSON()

		tokenID := uint64(tokenResp.Object().Value("id").Number().Raw())
		token := tokenResp.Object().Value("token").String().Raw()
		testData.UserActivationTokenIDs = append(testData.UserActivationTokenIDs, tokenID)
		t.Logf("Created UserActivationToken with ID: %d, Token: %s", tokenID, token)

		// Get UserActivationToken by ID
		e.GET(fmt.Sprintf("/accmgt/user-activation-token/%d", tokenID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("user_id").Number().IsEqual(float64(userID))

		// Get UserActivationToken by token
		e.GET(fmt.Sprintf("/accmgt/user-activation-token/token/%s", token)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("user_id").Number().IsEqual(float64(userID))

		// Get UserActivationToken by user ID
		e.GET(fmt.Sprintf("/accmgt/user-activation-token/user/%d", userID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("user_id").Number().IsEqual(float64(userID))

		// Update UserActivationToken (just update the token)
		updateData := model.UserActivationToken{
			UserID: userID,
			Token:  "updated-token-for-testing",
		}

		e.PUT(fmt.Sprintf("/accmgt/user-activation-token/%d", tokenID)).
			WithJSON(updateData).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("affected_rows").Number().Ge(0)

		// Verify update
		e.GET(fmt.Sprintf("/accmgt/user-activation-token/%d", tokenID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("token").String().IsEqual("updated-token-for-testing")

		// Delete UserActivationToken
		e.DELETE(fmt.Sprintf("/accmgt/user-activation-token/%d", tokenID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("affected_rows").Number().Ge(0)

		// Verify deletion
		e.GET(fmt.Sprintf("/accmgt/user-activation-token/%d", tokenID)).
			Expect().
			Status(http.StatusNotFound)

		t.Log("UserActivationToken CRUD operations passed!")
	})

	// Test error cases
	t.Run("UserActivationTokenErrors", func(t *testing.T) {
		// Test creating token for non-existent user
		e.POST("/accmgt/user-activation-token/99999").
			WithJSON(struct{}{}).
			Expect().
			Status(http.StatusBadRequest)

		// Test getting non-existent token
		e.GET("/accmgt/user-activation-token/99999").
			Expect().
			Status(http.StatusNotFound)

		// Test getting by invalid token
		e.GET("/accmgt/user-activation-token/token/invalid-token").
			Expect().
			Status(http.StatusNotFound)

		// Test getting by non-existent user ID
		e.GET("/accmgt/user-activation-token/user/99999").
			Expect().
			Status(http.StatusNotFound)

		t.Log("UserActivationToken error handling passed!")
	})
}

// Test UserActivationToken edge cases
func TestUserActivationTokenEdgeCases(t *testing.T) {
	defer cleanupTestData()
	e := getExpect(t)

	timestamp := time.Now().Unix()

	t.Run("MultipleTokensPerUser", func(t *testing.T) {
		// Create account and user
		account := model.Account{
			NameRegistered: fmt.Sprintf("Multi Token Test Company %d", timestamp),
			NameShort:      fmt.Sprintf("multitokenco%d", timestamp),
			PhoneNumber:    "555-5555",
		}

		accResp := e.POST("/accmgt/account").
			WithJSON(account).
			Expect().
			Status(http.StatusOK).
			JSON()

		accountID := uint64(accResp.Object().Value("id").Number().Raw())
		testData.AccountIDs = append(testData.AccountIDs, accountID)

		user := model.User{
			AccID:       accountID,
			RoleID:      10,
			UserStateID: 10,
			NameFirst:   "MultiToken",
			NameLast:    "User",
			Email:       fmt.Sprintf("<EMAIL>", timestamp),
		}

		userResp := e.POST("/accmgt/user").
			WithJSON(user).
			Expect().
			Status(http.StatusOK).
			JSON()

		userID := uint64(userResp.Object().Value("id").Number().Raw())
		testData.UserIDs = append(testData.UserIDs, userID)

		// Create first token
		token1Resp := e.POST(fmt.Sprintf("/accmgt/user-activation-token/%d", userID)).
			WithJSON(struct{}{}).
			Expect().
			Status(http.StatusOK).
			JSON()

		token1ID := uint64(token1Resp.Object().Value("id").Number().Raw())
		testData.UserActivationTokenIDs = append(testData.UserActivationTokenIDs, token1ID)

		// Try to create second token for same user (should work or handle appropriately)
		token2Resp := e.POST(fmt.Sprintf("/accmgt/user-activation-token/%d", userID)).
			WithJSON(struct{}{}).
			Expect()

		// Check if it's allowed or returns conflict
		if token2Resp.Raw().StatusCode == http.StatusOK {
			token2ID := uint64(token2Resp.JSON().Object().Value("id").Number().Raw())
			testData.UserActivationTokenIDs = append(testData.UserActivationTokenIDs, token2ID)
			t.Log("Multiple tokens per user allowed")
		} else if token2Resp.Raw().StatusCode == http.StatusConflict {
			t.Log("Multiple tokens per user not allowed (conflict)")
		} else {
			t.Logf("Unexpected status for second token: %d", token2Resp.Raw().StatusCode)
		}

		t.Log("Multiple tokens test completed!")
	})
}
