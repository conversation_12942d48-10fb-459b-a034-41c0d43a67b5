package main

import (
	"fmt"
	"log"

	"github.com/homewizeAI/accmgtms/dao"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/dbutil"
)

func main() {
	// Initialize database connection
	dbutil.Init()
	defer dbutil.Close()

	fmt.Println("Testing Policy DAO functions...")

	// Test PolicyCreate
	fmt.Println("\n1. Testing PolicyCreate...")
	testPolicy := apitypes.IDNameType{Name: "Test Policy"}
	id, err := dao.PolicyCreate(testPolicy)
	if err != nil {
		log.Fatal("PolicyCreate failed:", err)
	}
	fmt.Printf("Created policy with ID: %d\n", id)

	// Test PolicyGetByID
	fmt.Println("\n2. Testing PolicyGetByID...")
	policy, err := dao.PolicyGetByID(id)
	if err != nil {
		log.Fatal("PolicyGetByID failed:", err)
	}
	fmt.Printf("Retrieved policy: ID=%d, Name=%s\n", policy.ID, policy.Name)

	// Test PolicyGetByName
	fmt.Println("\n3. Testing PolicyGetByName...")
	policy2, err := dao.PolicyGetByName("Test Policy")
	if err != nil {
		log.Fatal("PolicyGetByName failed:", err)
	}
	fmt.Printf("Retrieved policy by name: ID=%d, Name=%s\n", policy2.ID, policy2.Name)

	// Test PolicyUpdate
	fmt.Println("\n4. Testing PolicyUpdate...")
	updateData := apitypes.IDNameType{Name: "Updated Test Policy"}
	affectedRows, err := dao.PolicyUpdate(id, updateData)
	if err != nil {
		log.Fatal("PolicyUpdate failed:", err)
	}
	fmt.Printf("Updated policy, affected rows: %d\n", affectedRows)

	// Verify update
	updatedPolicy, err := dao.PolicyGetByID(id)
	if err != nil {
		log.Fatal("Failed to get updated policy:", err)
	}
	fmt.Printf("Verified update: ID=%d, Name=%s\n", updatedPolicy.ID, updatedPolicy.Name)

	// Test PolicyList
	fmt.Println("\n5. Testing PolicyList...")
	page := apitypes.Page{Page: 1, Size: 10}
	policies, err := dao.PolicyList(page)
	if err != nil {
		log.Fatal("PolicyList failed:", err)
	}
	fmt.Printf("Retrieved %d policies\n", len(policies))
	for _, p := range policies {
		fmt.Printf("  - ID=%d, Name=%s\n", p.ID, p.Name)
	}

	// Test PolicyDelete
	fmt.Println("\n6. Testing PolicyDelete...")
	affectedRows, err = dao.PolicyDelete(id)
	if err != nil {
		log.Fatal("PolicyDelete failed:", err)
	}
	fmt.Printf("Deleted policy, affected rows: %d\n", affectedRows)

	// Verify deletion
	_, err = dao.PolicyGetByID(id)
	if err == nil {
		log.Fatal("Policy should have been deleted but still exists")
	}
	fmt.Println("Verified deletion: policy no longer exists")

	fmt.Println("\nAll Policy DAO tests passed successfully!")
}
