package api

import (
	"github.com/gin-gonic/gin"

	"github.com/homewizeAI/accmgtms/dao"
	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/errs"
)

func Signup(c *gin.Context) {
	var err error
	var data model.SignupRequest
	if err = c.BindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	response, err := dao.Signup(data)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		if errs.IsConflict(err) {
			apicommon.RespConflict(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err.Error())
		return
	}

	apicommon.RespOk(c, response)
}
