package client

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/homewizeAI/apitypes"
	"github.com/jarcoal/httpmock"
)

func TestUserStateCreate(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"POST",
		"http://localhost:3000/accmgt/user-state",
		func(req *http.Request) (*http.Response, error) {
			var userState apitypes.IDNameType
			err := json.NewDecoder(req.Body).Decode(&userState)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			var resp apitypes.IDType
			resp.ID = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	var userState apitypes.IDNameType
	userState.Name = "Test State"
	id, err := c.UserStateCreate(context.Background(), userState)
	if err != nil {
		t.Error(err)
	}
	if id != 1 {
		t.<PERSON>("Expected id 1, got %d", id)
	}
}

func TestUserStateGetByID(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/user-state/1",
		func(req *http.Request) (*http.Response, error) {
			userState := apitypes.IDNameType{
				ID:   1,
				Name: "Active",
			}
			return httpmock.NewJsonResponse(http.StatusOK, userState)
		},
	)

	userState, err := c.UserStateGetByID(context.Background(), 1)
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if userState == nil {
		t.Error("Expected user state response, got nil")
	}
}

func TestUserStateGetByName(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/user-state/name/Active",
		func(req *http.Request) (*http.Response, error) {
			userState := apitypes.IDNameType{
				ID:   1,
				Name: "Active",
			}
			return httpmock.NewJsonResponse(http.StatusOK, userState)
		},
	)

	userState, err := c.UserStateGetByName(context.Background(), "Active")
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if userState == nil {
		t.Error("Expected user state response, got nil")
	}
}

func TestUserStateUpdate(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"PUT",
		"http://localhost:3000/accmgt/user-state/1",
		func(req *http.Request) (*http.Response, error) {
			var userState apitypes.IDNameType
			err := json.NewDecoder(req.Body).Decode(&userState)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			var resp apitypes.AffectedRowsType
			resp.AffectedRows = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	var userState apitypes.IDNameType
	userState.Name = "Updated State"
	rowsAffected, err := c.UserStateUpdate(context.Background(), 1, userState)
	if err != nil {
		t.Error(err)
	}
	if rowsAffected != 1 {
		t.Errorf("Expected 1 row affected, got %d", rowsAffected)
	}
}

func TestUserStateDelete(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"DELETE",
		"http://localhost:3000/accmgt/user-state/1",
		func(req *http.Request) (*http.Response, error) {
			return httpmock.NewJsonResponse(http.StatusNoContent, nil)
		},
	)

	rowsAffected, err := c.UserStateDelete(context.Background(), 1)
	if err != nil {
		t.Error(err)
	}
	// For delete operations, we expect 0 rows affected when using 204 No Content
	_ = rowsAffected // Just verify we got a response without error
}

func TestUserStateList(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/user-states",
		func(req *http.Request) (*http.Response, error) {
			userStates := []apitypes.IDNameType{
				{ID: 1, Name: "Active"},
				{ID: 2, Name: "Locked"},
			}
			return httpmock.NewJsonResponse(http.StatusOK, userStates)
		},
	)

	page := apitypes.Page{Page: 0, Size: 20}
	userStates, err := c.UserStateList(context.Background(), page)
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if userStates == nil {
		t.Error("Expected user states response, got nil")
	}
}
