package tests

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
)

// Test AccState CRUD operations
func TestAccStateOperations(t *testing.T) {
	defer cleanupTestData()
	e := getExpect(t)

	timestamp := time.Now().Unix()

	t.Run("AccStateCRUD", func(t *testing.T) {
		// Create AccState
		accState := apitypes.IDNameType{
			Name: fmt.Sprintf("TestAccState%d", timestamp),
		}

		resp := e.POST("/accmgt/acc-state").
			WithJSON(accState).
			Expect().
			Status(http.StatusOK).
			JSON()

		accStateID := uint64(resp.Object().Value("id").Number().Raw())
		testData.AccStateIDs = append(testData.AccStateIDs, accStateID)
		t.Logf("Created AccState with ID: %d", accStateID)

		// Get AccState by ID
		e.GET(fmt.Sprintf("/accmgt/acc-state/%d", accStateID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("name").String().IsEqual(accState.Name)

		// Get AccState by name
		e.GET(fmt.Sprintf("/accmgt/acc-state/name/%s", accState.Name)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("name").String().IsEqual(accState.Name)

		// Update AccState
		updatedAccState := apitypes.IDNameType{
			Name: fmt.Sprintf("UpdatedAccState%d", timestamp),
		}

		e.PUT(fmt.Sprintf("/accmgt/acc-state/%d", accStateID)).
			WithJSON(updatedAccState).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("affected_rows").Number().Ge(0)

		// Verify update
		e.GET(fmt.Sprintf("/accmgt/acc-state/%d", accStateID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("name").String().IsEqual(updatedAccState.Name)

		// Delete AccState
		e.DELETE(fmt.Sprintf("/accmgt/acc-state/%d", accStateID)).
			Expect().
			Status(http.StatusNoContent)

		// Verify deletion
		e.GET(fmt.Sprintf("/accmgt/acc-state/%d", accStateID)).
			Expect().
			Status(http.StatusNotFound)

		t.Log("AccState CRUD operations passed!")
	})
}

// Test UserState CRUD operations
func TestUserStateOperations(t *testing.T) {
	defer cleanupTestData()
	e := getExpect(t)

	timestamp := time.Now().Unix()

	t.Run("UserStateCRUD", func(t *testing.T) {
		// Create UserState
		userState := apitypes.IDNameType{
			Name: fmt.Sprintf("TestUserState%d", timestamp),
		}

		resp := e.POST("/accmgt/user-state").
			WithJSON(userState).
			Expect().
			Status(http.StatusOK).
			JSON()

		userStateID := uint64(resp.Object().Value("id").Number().Raw())
		testData.UserStateIDs = append(testData.UserStateIDs, userStateID)
		t.Logf("Created UserState with ID: %d", userStateID)

		// Get UserState by ID
		e.GET(fmt.Sprintf("/accmgt/user-state/%d", userStateID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("name").String().IsEqual(userState.Name)

		// Get UserState by name
		e.GET(fmt.Sprintf("/accmgt/user-state/name/%s", userState.Name)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("name").String().IsEqual(userState.Name)

		// Update UserState
		updatedUserState := apitypes.IDNameType{
			Name: fmt.Sprintf("UpdatedUserState%d", timestamp),
		}

		e.PUT(fmt.Sprintf("/accmgt/user-state/%d", userStateID)).
			WithJSON(updatedUserState).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("affected_rows").Number().Ge(0)

		// Verify update
		e.GET(fmt.Sprintf("/accmgt/user-state/%d", userStateID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("name").String().IsEqual(updatedUserState.Name)

		// Delete UserState
		e.DELETE(fmt.Sprintf("/accmgt/user-state/%d", userStateID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("affected_rows").Number().Ge(0)

		// Verify deletion
		e.GET(fmt.Sprintf("/accmgt/user-state/%d", userStateID)).
			Expect().
			Status(http.StatusNotFound)

		t.Log("UserState CRUD operations passed!")
	})
}

// Test AccStateReason CRUD operations
func TestAccStateReasonOperations(t *testing.T) {
	defer cleanupTestData()
	e := getExpect(t)

	timestamp := time.Now().Unix()

	t.Run("AccStateReasonCRUD", func(t *testing.T) {
		// Create AccStateReason
		accStateReason := model.AccStateReason{
			AccStateID: 10, // Active state from seed data
			Reason:     fmt.Sprintf("TestReason%d", timestamp),
		}

		resp := e.POST("/accmgt/acc-state-reason").
			WithJSON(accStateReason).
			Expect().
			Status(http.StatusOK).
			JSON()

		accStateReasonID := uint64(resp.Object().Value("id").Number().Raw())
		testData.AccStateReasonIDs = append(testData.AccStateReasonIDs, accStateReasonID)
		t.Logf("Created AccStateReason with ID: %d", accStateReasonID)

		// Get AccStateReason by ID
		e.GET(fmt.Sprintf("/accmgt/acc-state-reason/%d", accStateReasonID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("reason").String().IsEqual(accStateReason.Reason)

		// Update AccStateReason
		updatedAccStateReason := model.AccStateReason{
			AccStateID: 10,
			Reason:     fmt.Sprintf("UpdatedReason%d", timestamp),
		}

		e.PUT(fmt.Sprintf("/accmgt/acc-state-reason/%d", accStateReasonID)).
			WithJSON(updatedAccStateReason).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("affected_rows").Number().Ge(0)

		// Verify update
		e.GET(fmt.Sprintf("/accmgt/acc-state-reason/%d", accStateReasonID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("reason").String().IsEqual(updatedAccStateReason.Reason)

		// Delete AccStateReason
		e.DELETE(fmt.Sprintf("/accmgt/acc-state-reason/%d", accStateReasonID)).
			Expect().
			Status(http.StatusOK).
			JSON().Object().Value("affected_rows").Number().Ge(0)

		// Verify deletion
		e.GET(fmt.Sprintf("/accmgt/acc-state-reason/%d", accStateReasonID)).
			Expect().
			Status(http.StatusNotFound)

		t.Log("AccStateReason CRUD operations passed!")
	})

	// Test AccStateReason list
	t.Run("AccStateReasonList", func(t *testing.T) {
		e.GET("/accmgt/acc-state-reasons").
			WithQuery("page", 0).
			WithQuery("size", 20).
			Expect().
			Status(http.StatusOK).
			JSON().Array().Length().Ge(1)

		t.Log("AccStateReason list operations passed!")
	})
}
