package api

import (
	"github.com/gin-gonic/gin"
	"github.com/homewizeAI/accmgtms/dao"
	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/errs"
	"github.com/homewizeAI/utils"
)

func UserPolicyCreate(c *gin.Context) {
	var req model.UserPolicyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		apicommon.RespBadRequest(c, "request")
		return
	}

	err := dao.UserPolicyCreate(req)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		if errs.IsConflict(err) {
			apicommon.RespConflict(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, gin.H{"message": "User policy link created successfully"})
}

func UserPolicyGetByUserID(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := utils.AsUint64(userIDStr)
	if err != nil {
		apicommon.RespBadRequest(c, "user_id")
		return
	}

	userPolicies, err := dao.UserPolicyGetByUserID(userID)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	// Ensure we return an empty array instead of null
	if userPolicies == nil {
		userPolicies = []model.UserPolicy{}
	}

	apicommon.RespOk(c, userPolicies)
}

func UserPolicyGetByPolicyID(c *gin.Context) {
	policyIDStr := c.Param("policy_id")
	policyID, err := utils.AsUint64(policyIDStr)
	if err != nil {
		apicommon.RespBadRequest(c, "policy_id")
		return
	}

	userPolicies, err := dao.UserPolicyGetByPolicyID(policyID)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	// Ensure we return an empty array instead of null
	if userPolicies == nil {
		userPolicies = []model.UserPolicy{}
	}

	apicommon.RespOk(c, userPolicies)
}

func UserPolicyDelete(c *gin.Context) {
	userIDStr := c.Param("user_id")
	policyIDStr := c.Param("policy_id")

	userID, err := utils.AsUint64(userIDStr)
	if err != nil {
		apicommon.RespBadRequest(c, "user_id")
		return
	}

	policyID, err := utils.AsUint64(policyIDStr)
	if err != nil {
		apicommon.RespBadRequest(c, "policy_id")
		return
	}

	affectedRows, err := dao.UserPolicyDelete(userID, policyID)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(affectedRows))
}

func UserPolicyList(c *gin.Context) {
	page := apicommon.GetPage(c)
	userPolicies, err := dao.UserPolicyList(page)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	// Ensure we return an empty array instead of null
	if userPolicies == nil {
		userPolicies = []model.UserPolicy{}
	}

	apicommon.RespOk(c, userPolicies)
}
