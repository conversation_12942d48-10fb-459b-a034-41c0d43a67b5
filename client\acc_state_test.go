package client

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/homewizeAI/apitypes"
	"github.com/jarcoal/httpmock"
)

func TestAccStateCreate(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"POST",
		"http://localhost:3000/accmgt/acc-state",
		func(req *http.Request) (*http.Response, error) {
			var accState apitypes.IDNameType
			err := json.NewDecoder(req.Body).Decode(&accState)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			var resp apitypes.IDType
			resp.ID = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	var accState apitypes.IDNameType
	accState.Name = "Active"
	id, err := c.AccStateCreate(context.Background(), accState)
	if err != nil {
		t.Error(err)
	}
	if id != 1 {
		t.<PERSON><PERSON><PERSON>("Expected id 1, got %d", id)
	}
}

func TestAccStateGetByID(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/acc-state/1",
		func(req *http.Request) (*http.Response, error) {
			accState := apitypes.IDNameType{
				ID:   1,
				Name: "Active",
			}
			return httpmock.NewJsonResponse(http.StatusOK, accState)
		},
	)

	accState, err := c.AccStateGetByID(context.Background(), 1)
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if accState == nil {
		t.Error("Expected acc state response, got nil")
	}
}

func TestAccStateGetByName(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/acc-state/name/Active",
		func(req *http.Request) (*http.Response, error) {
			accState := apitypes.IDNameType{
				ID:   1,
				Name: "Active",
			}
			return httpmock.NewJsonResponse(http.StatusOK, accState)
		},
	)

	accState, err := c.AccStateGetByName(context.Background(), "Active")
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if accState == nil {
		t.Error("Expected acc state response, got nil")
	}
}

func TestAccStateUpdate(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"PUT",
		"http://localhost:3000/accmgt/acc-state/1",
		func(req *http.Request) (*http.Response, error) {
			var accState apitypes.IDNameType
			err := json.NewDecoder(req.Body).Decode(&accState)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			var resp apitypes.AffectedRowsType
			resp.AffectedRows = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	var accState apitypes.IDNameType
	accState.Name = "Inactive"
	rowsAffected, err := c.AccStateUpdate(context.Background(), 1, accState)
	if err != nil {
		t.Error(err)
	}
	if rowsAffected != 1 {
		t.Errorf("Expected 1 row affected, got %d", rowsAffected)
	}
}

func TestAccStateDelete(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"DELETE",
		"http://localhost:3000/accmgt/acc-state/1",
		func(req *http.Request) (*http.Response, error) {
			return httpmock.NewStringResponse(http.StatusNoContent, ""), nil
		},
	)

	err := c.AccStateDelete(context.Background(), 1)
	if err != nil {
		t.Error(err)
	}
}

func TestAccStateList(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/acc-states",
		func(req *http.Request) (*http.Response, error) {
			accStates := []apitypes.IDNameType{
				{ID: 1, Name: "Active"},
				{ID: 2, Name: "Inactive"},
			}
			return httpmock.NewJsonResponse(http.StatusOK, accStates)
		},
	)

	page := apitypes.Page{Page: 0, Size: 20}
	accStates, err := c.AccStateList(context.Background(), page)
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if accStates == nil {
		t.Error("Expected acc states response, got nil")
	}
}
