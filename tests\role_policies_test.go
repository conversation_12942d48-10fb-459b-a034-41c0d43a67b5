package tests

import (
	"fmt"
	"testing"
	"time"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
)

func TestRolePolicyCreate(t *testing.T) {
	e := getExpect(t)

	// Create test role and policy first
	role := model.Role{Name: "TestRole"}
	roleResp := e.POST("/accmgt/role").
		WithJSON(role).
		Expect().
		Status(200).
		JSON().Object()
	roleID := uint64(roleResp.Value("id").Number().Raw())
	testData.RoleIDs = append(testData.RoleIDs, roleID)

	policy := apitypes.IDNameType{Name: "TestPolicy"}
	policyResp := e.POST("/accmgt/policy").
		WithJSON(policy).
		Expect().
		Status(200).
		JSON().Object()
	policyID := uint64(policyResp.Value("id").Number().Raw())
	testData.PolicyIDs = append(testData.PolicyIDs, policyID)

	// Create role-policy link
	rolePolicy := model.RolePolicyRequest{
		RoleID:   roleID,
		PolicyID: policyID,
	}

	e.POST("/accmgt/role-policy").
		WithJSON(rolePolicy).
		Expect().
		Status(200).
		JSON().Object().
		ContainsKey("message")
}

func TestRolePolicyCreateValidation(t *testing.T) {
	e := getExpect(t)

	// Test missing role_id
	rolePolicy := model.RolePolicyRequest{
		PolicyID: 1,
	}
	e.POST("/accmgt/role-policy").
		WithJSON(rolePolicy).
		Expect().
		Status(400)

	// Test missing policy_id
	rolePolicy = model.RolePolicyRequest{
		RoleID: 1,
	}
	e.POST("/accmgt/role-policy").
		WithJSON(rolePolicy).
		Expect().
		Status(400)

	// Test invalid foreign key
	rolePolicy = model.RolePolicyRequest{
		RoleID:   99999,
		PolicyID: 99999,
	}
	e.POST("/accmgt/role-policy").
		WithJSON(rolePolicy).
		Expect().
		Status(400)
}

func TestRolePolicyGetByRoleID(t *testing.T) {
	e := getExpect(t)

	// Create test role and policy
	timestamp := time.Now().UnixNano()
	role := model.Role{Name: fmt.Sprintf("TestRole%d", timestamp)}
	roleResp := e.POST("/accmgt/role").
		WithJSON(role).
		Expect().
		Status(200).
		JSON().Object()
	roleID := uint64(roleResp.Value("id").Number().Raw())
	testData.RoleIDs = append(testData.RoleIDs, roleID)

	policy := apitypes.IDNameType{Name: "TestPolicy2"}
	policyResp := e.POST("/accmgt/policy").
		WithJSON(policy).
		Expect().
		Status(200).
		JSON().Object()
	policyID := uint64(policyResp.Value("id").Number().Raw())
	testData.PolicyIDs = append(testData.PolicyIDs, policyID)

	// Create role-policy link
	rolePolicy := model.RolePolicyRequest{
		RoleID:   roleID,
		PolicyID: policyID,
	}
	e.POST("/accmgt/role-policy").
		WithJSON(rolePolicy).
		Expect().
		Status(200)

	// Get by role ID
	resp := e.GET("/accmgt/role-policy/role/{role_id}", roleID).
		Expect().
		Status(200).
		JSON().Array()

	resp.Length().Ge(1)
	resp.Element(0).Object().
		Value("role_id").Number().Equal(roleID)
	resp.Element(0).Object().
		Value("policy_id").Number().Equal(policyID)
}

func TestRolePolicyGetByPolicyID(t *testing.T) {
	e := getExpect(t)

	// Create test role and policy
	timestamp := time.Now().UnixNano()
	role := model.Role{Name: fmt.Sprintf("TestRole%d", timestamp)}
	roleResp := e.POST("/accmgt/role").
		WithJSON(role).
		Expect().
		Status(200).
		JSON().Object()
	roleID := uint64(roleResp.Value("id").Number().Raw())
	testData.RoleIDs = append(testData.RoleIDs, roleID)

	policy := apitypes.IDNameType{Name: "TestPolicy3"}
	policyResp := e.POST("/accmgt/policy").
		WithJSON(policy).
		Expect().
		Status(200).
		JSON().Object()
	policyID := uint64(policyResp.Value("id").Number().Raw())
	testData.PolicyIDs = append(testData.PolicyIDs, policyID)

	// Create role-policy link
	rolePolicy := model.RolePolicyRequest{
		RoleID:   roleID,
		PolicyID: policyID,
	}
	e.POST("/accmgt/role-policy").
		WithJSON(rolePolicy).
		Expect().
		Status(200)

	// Get by policy ID
	resp := e.GET("/accmgt/role-policy/policy/{policy_id}", policyID).
		Expect().
		Status(200).
		JSON().Array()

	resp.Length().Ge(1)
	resp.Element(0).Object().
		Value("role_id").Number().Equal(roleID)
	resp.Element(0).Object().
		Value("policy_id").Number().Equal(policyID)
}

func TestRolePolicyDelete(t *testing.T) {
	e := getExpect(t)

	// Create test role and policy
	timestamp := time.Now().UnixNano()
	role := model.Role{Name: fmt.Sprintf("TestRole%d", timestamp)}
	roleResp := e.POST("/accmgt/role").
		WithJSON(role).
		Expect().
		Status(200).
		JSON().Object()
	roleID := uint64(roleResp.Value("id").Number().Raw())
	testData.RoleIDs = append(testData.RoleIDs, roleID)

	policy := apitypes.IDNameType{Name: "TestPolicy4"}
	policyResp := e.POST("/accmgt/policy").
		WithJSON(policy).
		Expect().
		Status(200).
		JSON().Object()
	policyID := uint64(policyResp.Value("id").Number().Raw())
	testData.PolicyIDs = append(testData.PolicyIDs, policyID)

	// Create role-policy link
	rolePolicy := model.RolePolicyRequest{
		RoleID:   roleID,
		PolicyID: policyID,
	}
	e.POST("/accmgt/role-policy").
		WithJSON(rolePolicy).
		Expect().
		Status(200)

	// Delete the link
	e.DELETE("/accmgt/role-policy/role/{role_id}/policy/{policy_id}", roleID, policyID).
		Expect().
		Status(200).
		JSON().Object().
		Value("affected_rows").Number().Equal(1)

	// Verify deletion - should return empty array
	e.GET("/accmgt/role-policy/role/{role_id}", roleID).
		Expect().
		Status(200).
		JSON().Array().
		Length().Equal(0)
}

func TestRolePolicyList(t *testing.T) {
	e := getExpect(t)

	// Get list
	resp := e.GET("/accmgt/role-policies").
		WithQuery("page", 0).
		WithQuery("size", 20).
		Expect().
		Status(200).
		JSON().Array()

	// Should be an array (might be empty)
	resp.Length().Ge(0)
}
