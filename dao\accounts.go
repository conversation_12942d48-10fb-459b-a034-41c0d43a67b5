package dao

import (
	"database/sql"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/dbutil"
	"github.com/homewizeAI/errs"
)

const (
	selAccount = `select id, acc_state_id, acc_state_reason_id, name_registered, name_short, addr_line1, addr_line2,
		       addr_city, addr_state, addr_country, addr_postal_code,
		       phone_cc, phone_number, created_at, updated_at from accounts `
)

// scanRowToAccount scans a database row into an Account struct
func scanRowToAccount(scanner dbutil.Scanner) (*model.Account, error) {
	var acc model.Account
	var accStateID sql.NullInt64
	var accStateReasonID sql.NullInt64
	err := scanner.Scan(
		&acc.ID,
		&accStateID,
		&accStateReasonID,
		&acc.NameRegistered,
		&acc.NameShort,
		&acc.AddrLine1,
		&acc.AddrLine2,
		&acc.AddrCity,
		&acc.AddrState,
		&acc.AddrCountry,
		&acc.AddrPostalCode,
		&acc.PhoneCC,
		&acc.PhoneNumber,
		&acc.CreatedAt,
		&acc.UpdatedAt,
	)
	if err != nil {
		return nil, err
	}
	if accStateID.Valid {
		val := uint64(accStateID.Int64)
		acc.AccStateID = &val
	}
	if accStateReasonID.Valid {
		val := uint64(accStateReasonID.Int64)
		acc.AccStateReasonID = &val
	}
	return &acc, nil
}

func AccCreate(data model.Account) (uint64, error) {
	return accCreate(nil, data)
}

// accCreate creates an account with optional transaction
func accCreate(tx *dbutil.Transaction, data model.Account) (uint64, error) {
	// Set default values for state fields if they are nil

	var accStateID sql.NullInt64
	if data.AccStateID == nil {
		accStateID.Valid = false
	} else {
		accStateID.Valid = true
		accStateID.Int64 = int64(*data.AccStateID)
	}

	var accStateReasonID sql.NullInt64
	if data.AccStateReasonID == nil {
		accStateReasonID.Valid = false
	} else {
		accStateReasonID.Valid = true
		accStateReasonID.Int64 = int64(*data.AccStateReasonID)
	}

	query := `
		INSERT INTO accounts (
			acc_state_id,
			acc_state_reason_id,
			name_registered,
			name_short,
			addr_line1,
			addr_line2,
			addr_city,
			addr_state,
			addr_country,
			addr_postal_code,
			phone_cc,
			phone_number
		) VALUES (
			$1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12
		) RETURNING id
	`

	var id uint64
	var err error

	if tx != nil {
		// For transactions, we need to use the transaction's connection
		// Let's use ExecGetID for now and see if dbutil handles RETURNING
		id, err = tx.ExecGetID(
			query,
			accStateID,
			accStateReasonID,
			data.NameRegistered,
			data.NameShort,
			data.AddrLine1,
			data.AddrLine2,
			data.AddrCity,
			data.AddrState,
			data.AddrCountry,
			data.AddrPostalCode,
			data.PhoneCC,
			data.PhoneNumber,
		)
	} else {
		err = dbutil.QueryRow(
			query,
			accStateID,
			accStateReasonID,
			data.NameRegistered,
			data.NameShort,
			data.AddrLine1,
			data.AddrLine2,
			data.AddrCity,
			data.AddrState,
			data.AddrCountry,
			data.AddrPostalCode,
			data.PhoneCC,
			data.PhoneNumber,
		).Scan(&id)
	}

	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, errs.BadInput("relationship")
		}
		if dbutil.ErrIsUniqueViolation(err) {
			return 0, errs.BadInput("short-name")
		}
		return 0, err
	}

	return id, nil
}

// AccGetByID retrieves an account by its ID
func AccGetByID(id uint64) (*model.Account, error) {
	query := selAccount + ` WHERE id = $1 `
	row := dbutil.QueryRow(query, id)
	acc, err := scanRowToAccount(row)
	if err != nil {
		// Debug: Log the actual error type and message
		// fmt.Printf("DEBUG AccGetByID: err=%v, type=%T, sql.ErrNoRows=%v, equal=%v\n", err, err, sql.ErrNoRows, err == sql.ErrNoRows)
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("account")
		}
		return nil, err
	}

	return acc, nil
}

// AccGetByShortName retrieves an account by its short name
func AccGetByShortName(shortName string) (*model.Account, error) {
	query := selAccount + ` WHERE name_short = $1 `
	row := dbutil.QueryRow(query, shortName)
	acc, err := scanRowToAccount(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("account")
		}
		return nil, err
	}

	return acc, nil
}

// AccUpdate updates an existing account
func AccUpdate(id uint64, data model.Account) (uint64, error) {
	// Set default values for state fields if they are nil
	accStateID := data.AccStateID
	if accStateID == nil {
		defaultStateID := uint64(10) // Active state
		accStateID = &defaultStateID
	}

	accStateReasonID := data.AccStateReasonID
	if accStateReasonID == nil {
		defaultReasonID := uint64(10) // Email Validated reason
		accStateReasonID = &defaultReasonID
	}

	query := `
		UPDATE accounts SET
			acc_state_id = $1,
			acc_state_reason_id = $2,
			name_registered = $3,
			name_short = $4,
			addr_line1 = $5,
			addr_line2 = $6,
			addr_city = $7,
			addr_state = $8,
			addr_country = $9,
			addr_postal_code = $10,
			phone_cc = $11,
			phone_number = $12,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = $13
	`

	rowsAffected, err := dbutil.ExecGetAffRows(
		query,
		accStateID,
		accStateReasonID,
		data.NameRegistered,
		data.NameShort,
		data.AddrLine1,
		data.AddrLine2,
		data.AddrCity,
		data.AddrState,
		data.AddrCountry,
		data.AddrPostalCode,
		data.PhoneCC,
		data.PhoneNumber,
		id,
	)

	if err != nil {
		if dbutil.ErrIsUniqueViolation(err) {
			return 0, errs.BadInput("short-name")
		}
		return 0, err
	}

	return rowsAffected, nil
}

// AccDelete deletes an account by ID
func AccDelete(id uint64) (uint64, error) {
	query := `DELETE FROM accounts WHERE id = $1`

	rowsAffected, err := dbutil.ExecGetAffRows(query, id)
	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, errs.BadInput("account-in-use")
		}
		return 0, err
	}

	return rowsAffected, nil
}

// AccList retrieves a list of accounts with pagination
func AccList(page apitypes.Page) ([]*model.Account, error) {
	query := selAccount + ` ORDER BY created_at DESC ` + page.AsSQLLimit()

	rows, err := dbutil.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var accounts []*model.Account
	for rows.Next() {
		acc, err := scanRowToAccount(rows)
		if err != nil {
			return nil, err
		}
		accounts = append(accounts, acc)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return accounts, nil
}

// AccCount returns the total number of accounts
func AccCount() (uint64, error) {
	query := `SELECT COUNT(*) FROM accounts`
	return dbutil.QueryCount(query)
}

// AccCheckShortNameAvailable checks if an account short name is available
func AccCheckShortNameAvailable(shortName string) (bool, error) {
	query := `SELECT COUNT(*) FROM accounts WHERE name_short = $1`
	count, err := dbutil.QueryCount(query, shortName)
	if err != nil {
		return false, err
	}
	return count == 0, nil
}
