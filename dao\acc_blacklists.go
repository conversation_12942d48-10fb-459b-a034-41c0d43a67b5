package dao

import (
	"database/sql"

	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/dbutil"
	"github.com/homewizeAI/errs"
)

const (
	selAccBlacklist = `SELECT id, name FROM acc_blacklists `
)

// scanRowToAccBlacklist scans a database row into an IDNameType struct
func scanRowToAccBlacklist(scanner dbutil.Scanner) (*apitypes.IDNameType, error) {
	var accBlacklist apitypes.IDNameType
	err := scanner.Scan(
		&accBlacklist.ID,
		&accBlacklist.Name,
	)
	if err != nil {
		return nil, err
	}
	return &accBlacklist, nil
}

func AccBlacklistCreate(data apitypes.IDNameType) (uint64, error) {
	query := `
		INSERT INTO acc_blacklists (
			name
		) VALUES (
			$1
		) RETURNING id
	`

	var id uint64
	err := dbutil.QueryRow(query, data.Name).Scan(&id)

	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, errs.BadInput("relationship")
		}
		if dbutil.ErrIsUniqueViolation(err) {
			return 0, errs.BadInput("name")
		}
		return 0, err
	}

	return id, nil
}

// AccBlacklistGetByID retrieves an acc_blacklist by its ID
func AccBlacklistGetByID(id uint64) (*apitypes.IDNameType, error) {
	query := selAccBlacklist + ` WHERE id = $1 `

	row := dbutil.QueryRow(query, id)

	accBlacklist, err := scanRowToAccBlacklist(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("acc-blacklist")
		}
		return nil, err
	}

	return accBlacklist, nil
}

// AccBlacklistGetByName retrieves an acc_blacklist by its name
func AccBlacklistGetByName(name string) (*apitypes.IDNameType, error) {
	query := selAccBlacklist + ` WHERE name = $1 `

	row := dbutil.QueryRow(query, name)

	accBlacklist, err := scanRowToAccBlacklist(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("acc-blacklist")
		}
		return nil, err
	}

	return accBlacklist, nil
}

// AccBlacklistUpdate updates an existing acc_blacklist
func AccBlacklistUpdate(id uint64, data apitypes.IDNameType) (uint64, error) {
	query := `
		UPDATE acc_blacklists SET
			name = $1,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = $2
	`

	rowsAffected, err := dbutil.ExecGetAffRows(
		query,
		data.Name,
		id,
	)

	if err != nil {
		if dbutil.ErrIsUniqueViolation(err) {
			return 0, errs.BadInput("name")
		}
		return 0, err
	}

	if rowsAffected == 0 {
		return 0, errs.NotFound("acc-blacklist")
	}

	return rowsAffected, nil
}

// AccBlacklistDelete deletes an acc_blacklist by ID
func AccBlacklistDelete(id uint64) (uint64, error) {
	query := `DELETE FROM acc_blacklists WHERE id = $1`

	rowsAffected, err := dbutil.ExecGetAffRows(query, id)
	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, errs.BadInput("acc-blacklist-in-use")
		}
		return 0, err
	}

	return rowsAffected, nil
}

// AccBlacklistList retrieves a list of acc_blacklists with pagination
func AccBlacklistList(page apitypes.Page) ([]*apitypes.IDNameType, error) {
	query := selAccBlacklist + ` ORDER BY name ASC ` + page.AsSQLLimit()

	rows, err := dbutil.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var accBlacklists []*apitypes.IDNameType
	for rows.Next() {
		accBlacklist, err := scanRowToAccBlacklist(rows)
		if err != nil {
			return nil, err
		}
		accBlacklists = append(accBlacklists, accBlacklist)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return accBlacklists, nil
}
