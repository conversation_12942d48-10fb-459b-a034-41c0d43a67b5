package dao

import (
	"database/sql"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/dbutil"
	"github.com/homewizeAI/errs"
)

const (
	selAccStateReason = `SELECT id, acc_state_id, reason, created_at, updated_at FROM acc_state_reason `
)

// scanRowToAccStateReason scans a database row into an AccStateReason struct
func scanRowToAccStateReason(scanner dbutil.Scanner) (*model.AccStateReason, error) {
	var accStateReason model.AccStateReason
	err := scanner.Scan(
		&accStateReason.ID,
		&accStateReason.AccStateID,
		&accStateReason.Reason,
		&accStateReason.CreatedAt,
		&accStateReason.UpdatedAt,
	)
	if err != nil {
		return nil, err
	}
	return &accStateReason, nil
}

func AccStateReasonCreate(data model.AccStateReason) (uint64, error) {
	query := `
		INSERT INTO acc_state_reason (
			acc_state_id,
			reason
		) VALUES (
			$1,$2
		)
	`

	id, err := dbutil.ExecGetID(
		query,
		data.AccStateID,
		data.Reason,
	)

	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, errs.BadInput("relationship")
		}
		return 0, err
	}

	return id, nil
}

// AccStateReasonGetByID retrieves an acc_state_reason by its ID
func AccStateReasonGetByID(id uint64) (*model.AccStateReason, error) {
	query := selAccStateReason + ` WHERE id = $1 `

	row := dbutil.QueryRow(query, id)

	accStateReason, err := scanRowToAccStateReason(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("acc-state-reason")
		}
		return nil, err
	}

	return accStateReason, nil
}

// AccStateReasonUpdate updates an existing acc_state_reason
// Does not return error if no rows are affected
func AccStateReasonUpdate(id uint64, data model.AccStateReason) (uint64, error) {
	query := `
		UPDATE acc_state_reason SET
			acc_state_id = $1,
			reason = $2,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = $3
	`

	rowsAffected, err := dbutil.ExecGetAffRows(
		query,
		data.AccStateID,
		data.Reason,
		id,
	)

	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, errs.BadInput("relationship")
		}
		return 0, err
	}

	// Note: Does not return error if rowsAffected == 0 as per requirements
	return rowsAffected, nil
}

// AccStateReasonDelete deletes an acc_state_reason by ID
// Returns rows affected and does not error if no rows are affected
func AccStateReasonDelete(id uint64) (uint64, error) {
	query := `DELETE FROM acc_state_reasons WHERE id = $1`

	rowsAffected, err := dbutil.ExecGetAffRows(query, id)
	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, errs.BadInput("acc-state-reason-in-use")
		}
		return 0, err
	}

	// Note: Does not return error if rowsAffected == 0 as per requirements
	return rowsAffected, nil
}

// AccStateReasonList retrieves a list of acc_state_reasons with pagination
func AccStateReasonList(page apitypes.Page) ([]*model.AccStateReason, error) {
	query := selAccStateReason + ` ORDER BY created_at DESC ` + page.AsSQLLimit()

	rows, err := dbutil.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var accStateReasons []*model.AccStateReason
	for rows.Next() {
		accStateReason, err := scanRowToAccStateReason(rows)
		if err != nil {
			return nil, err
		}
		accStateReasons = append(accStateReasons, accStateReason)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return accStateReasons, nil
}
