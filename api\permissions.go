package api

import (
	"github.com/gin-gonic/gin"
	"github.com/homewizeAI/accmgtms/dao"
	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/errs"
	"github.com/homewizeAI/utils"
)

func PermissionCreate(c *gin.Context) {
	var permission apitypes.IDNameType
	if err := c.ShouldBindJSON(&permission); err != nil {
		apicommon.RespBadRequest(c, "json")
		return
	}

	if permission.Name == "" {
		apicommon.RespBadRequest(c, "name")
		return
	}

	id, err := dao.PermissionCreate(permission)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, apitypes.IDTypeNew(id))
}

func PermissionGetByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	permission, err := dao.PermissionGetByID(id)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, permission)
}

func PermissionGetByName(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		apicommon.RespBadRequest(c, "name")
		return
	}

	permission, err := dao.PermissionGetByName(name)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, permission)
}

func PermissionUpdate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	var permission apitypes.IDNameType
	if err := c.ShouldBindJSON(&permission); err != nil {
		apicommon.RespBadRequest(c, "json")
		return
	}

	if permission.Name == "" {
		apicommon.RespBadRequest(c, "name")
		return
	}

	affectedRows, err := dao.PermissionUpdate(id, permission)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(affectedRows))
}

func PermissionDelete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	affectedRows, err := dao.PermissionDelete(id)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(affectedRows))
}

func PermissionList(c *gin.Context) {
	page := apicommon.GetPage(c)
	permissions, err := dao.PermissionList(page)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, permissions)
}
