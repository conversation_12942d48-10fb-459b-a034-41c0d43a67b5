package model

import (
	"time"
)

// PolicyPerm represents a link between a policy and a permission
type PolicyPerm struct {
	PolicyID     uint64    `json:"policy_id" db:"policy_id"`
	PermissionID uint64    `json:"permission_id" db:"permission_id"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
}

// PolicyPermRequest represents the request structure for creating policy-permission links
type PolicyPermRequest struct {
	PolicyID     uint64 `json:"policy_id" binding:"required"`
	PermissionID uint64 `json:"permission_id" binding:"required"`
}
