package api

import (
	"github.com/gin-gonic/gin"

	"github.com/homewizeAI/accmgtms/dao"
	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/errs"
	"github.com/homewizeAI/utils"
)

func AccBlacklistCreate(c *gin.Context) {
	var err error
	var data apitypes.IDNameType
	if err = c.BindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	// Validate required fields
	if data.Name == "" {
		apicommon.RespBadRequest(c, "name")
		return
	}

	id, err := dao.AccBlacklistCreate(data)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		if errs.IsConflict(err) {
			apicommon.RespConflict(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}
	apicommon.RespOk(c, apitypes.IDTypeNew(id))
}

func AccBlacklistGetByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	accBlacklist, err := dao.AccBlacklistGetByID(id)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, accBlacklist)
}

func AccBlacklistGetByName(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		apicommon.RespBadRequest(c, "name")
		return
	}

	accBlacklist, err := dao.AccBlacklistGetByName(name)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, accBlacklist)
}

func AccBlacklistUpdate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	var data apitypes.IDNameType
	if err = c.BindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	// Validate required fields
	if data.Name == "" {
		apicommon.RespBadRequest(c, "name")
		return
	}

	rowsAffected, err := dao.AccBlacklistUpdate(id, data)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(rowsAffected))
}

func AccBlacklistDelete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	rowsAffected, err := dao.AccBlacklistDelete(id)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(rowsAffected))
}

func AccBlacklistList(c *gin.Context) {
	page := apicommon.GetPage(c)
	accBlacklists, err := dao.AccBlacklistList(page)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, accBlacklists)
}
