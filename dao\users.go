package dao

import (
	"database/sql"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/dbutil"
	"github.com/homewizeAI/errs"
)

const (
	selUser = `select id, acc_id, role_id, user_state_id, name_first, name_middle, name_last, email,
		       addr_home_line1, addr_home_line2, addr_home_city, addr_home_state,
		       addr_home_country, addr_home_postal_code,
		       addr_work_line1, addr_work_line2, addr_work_city, addr_work_state,
		       addr_work_country, addr_work_postal_code,
		       phone_home_cc, phone_home_number, phone_work_cc, phone_work_number,
		       phone_mobile_cc, phone_mobile_number, created_at, updated_at from users `
)

// scanRowToUser scans a database row into a User struct
func scanRowToUser(scanner dbutil.Scanner) (*model.User, error) {
	var user model.User
	var nameMiddle sql.NullString
	var addrHomeLine1, addrHomeLine2, addrHomeCity, addrHomeState, addrHomeCountry, addrHomePostalCode sql.NullString
	var addrWorkLine1, addrWorkLine2, addrWorkCity, addrWorkState, addrWorkCountry, addrWorkPostalCode sql.NullString
	var phoneHomeCC, phoneHomeNumber, phoneWorkCC, phoneWorkNumber, phoneMobileCC, phoneMobileNumber sql.NullString

	err := scanner.Scan(
		&user.ID,
		&user.AccID,
		&user.RoleID,
		&user.UserStateID,
		&user.NameFirst,
		&nameMiddle,
		&user.NameLast,
		&user.Email,
		&addrHomeLine1,
		&addrHomeLine2,
		&addrHomeCity,
		&addrHomeState,
		&addrHomeCountry,
		&addrHomePostalCode,
		&addrWorkLine1,
		&addrWorkLine2,
		&addrWorkCity,
		&addrWorkState,
		&addrWorkCountry,
		&addrWorkPostalCode,
		&phoneHomeCC,
		&phoneHomeNumber,
		&phoneWorkCC,
		&phoneWorkNumber,
		&phoneMobileCC,
		&phoneMobileNumber,
		&user.CreatedAt,
		&user.UpdatedAt,
	)
	if err != nil {
		return nil, err
	}

	// Handle nullable fields
	if nameMiddle.Valid {
		user.NameMiddle = &nameMiddle.String
	}
	if addrHomeLine1.Valid {
		user.AddrHomeLine1 = &addrHomeLine1.String
	}
	if addrHomeLine2.Valid {
		user.AddrHomeLine2 = &addrHomeLine2.String
	}
	if addrHomeCity.Valid {
		user.AddrHomeCity = &addrHomeCity.String
	}
	if addrHomeState.Valid {
		user.AddrHomeState = &addrHomeState.String
	}
	if addrHomeCountry.Valid {
		user.AddrHomeCountry = &addrHomeCountry.String
	}
	if addrHomePostalCode.Valid {
		user.AddrHomePostalCode = &addrHomePostalCode.String
	}
	if addrWorkLine1.Valid {
		user.AddrWorkLine1 = &addrWorkLine1.String
	}
	if addrWorkLine2.Valid {
		user.AddrWorkLine2 = &addrWorkLine2.String
	}
	if addrWorkCity.Valid {
		user.AddrWorkCity = &addrWorkCity.String
	}
	if addrWorkState.Valid {
		user.AddrWorkState = &addrWorkState.String
	}
	if addrWorkCountry.Valid {
		user.AddrWorkCountry = &addrWorkCountry.String
	}
	if addrWorkPostalCode.Valid {
		user.AddrWorkPostalCode = &addrWorkPostalCode.String
	}
	if phoneHomeCC.Valid {
		user.PhoneHomeCC = &phoneHomeCC.String
	}
	if phoneHomeNumber.Valid {
		user.PhoneHomeNumber = &phoneHomeNumber.String
	}
	if phoneWorkCC.Valid {
		user.PhoneWorkCC = &phoneWorkCC.String
	}
	if phoneWorkNumber.Valid {
		user.PhoneWorkNumber = &phoneWorkNumber.String
	}
	if phoneMobileCC.Valid {
		user.PhoneMobileCC = &phoneMobileCC.String
	}
	if phoneMobileNumber.Valid {
		user.PhoneMobileNumber = &phoneMobileNumber.String
	}

	return &user, nil
}

func UserCreate(data model.User) (uint64, error) {
	return userCreate(nil, data)
}

// userCreate creates a user with optional transaction
func userCreate(tx *dbutil.Transaction, data model.User) (uint64, error) {
	// Set default values for required fields if they are 0
	roleID := data.RoleID
	if roleID == 0 {
		roleID = 10 // Default to Admin role
	}

	userStateID := data.UserStateID
	if userStateID == 0 {
		userStateID = 10 // Default to Not Validated state
	}

	// Convert pointer fields to sql.Null types
	var nameMiddle sql.NullString
	if data.NameMiddle != nil {
		nameMiddle.Valid = true
		nameMiddle.String = *data.NameMiddle
	}

	var addrHomeLine1, addrHomeLine2, addrHomeCity, addrHomeState, addrHomeCountry, addrHomePostalCode sql.NullString
	if data.AddrHomeLine1 != nil {
		addrHomeLine1.Valid = true
		addrHomeLine1.String = *data.AddrHomeLine1
	}
	if data.AddrHomeLine2 != nil {
		addrHomeLine2.Valid = true
		addrHomeLine2.String = *data.AddrHomeLine2
	}
	if data.AddrHomeCity != nil {
		addrHomeCity.Valid = true
		addrHomeCity.String = *data.AddrHomeCity
	}
	if data.AddrHomeState != nil {
		addrHomeState.Valid = true
		addrHomeState.String = *data.AddrHomeState
	}
	if data.AddrHomeCountry != nil {
		addrHomeCountry.Valid = true
		addrHomeCountry.String = *data.AddrHomeCountry
	}
	if data.AddrHomePostalCode != nil {
		addrHomePostalCode.Valid = true
		addrHomePostalCode.String = *data.AddrHomePostalCode
	}

	var addrWorkLine1, addrWorkLine2, addrWorkCity, addrWorkState, addrWorkCountry, addrWorkPostalCode sql.NullString
	if data.AddrWorkLine1 != nil {
		addrWorkLine1.Valid = true
		addrWorkLine1.String = *data.AddrWorkLine1
	}
	if data.AddrWorkLine2 != nil {
		addrWorkLine2.Valid = true
		addrWorkLine2.String = *data.AddrWorkLine2
	}
	if data.AddrWorkCity != nil {
		addrWorkCity.Valid = true
		addrWorkCity.String = *data.AddrWorkCity
	}
	if data.AddrWorkState != nil {
		addrWorkState.Valid = true
		addrWorkState.String = *data.AddrWorkState
	}
	if data.AddrWorkCountry != nil {
		addrWorkCountry.Valid = true
		addrWorkCountry.String = *data.AddrWorkCountry
	}
	if data.AddrWorkPostalCode != nil {
		addrWorkPostalCode.Valid = true
		addrWorkPostalCode.String = *data.AddrWorkPostalCode
	}

	var phoneHomeCC, phoneHomeNumber, phoneWorkCC, phoneWorkNumber, phoneMobileCC, phoneMobileNumber sql.NullString
	if data.PhoneHomeCC != nil {
		phoneHomeCC.Valid = true
		phoneHomeCC.String = *data.PhoneHomeCC
	}
	if data.PhoneHomeNumber != nil {
		phoneHomeNumber.Valid = true
		phoneHomeNumber.String = *data.PhoneHomeNumber
	}
	if data.PhoneWorkCC != nil {
		phoneWorkCC.Valid = true
		phoneWorkCC.String = *data.PhoneWorkCC
	}
	if data.PhoneWorkNumber != nil {
		phoneWorkNumber.Valid = true
		phoneWorkNumber.String = *data.PhoneWorkNumber
	}
	if data.PhoneMobileCC != nil {
		phoneMobileCC.Valid = true
		phoneMobileCC.String = *data.PhoneMobileCC
	}
	if data.PhoneMobileNumber != nil {
		phoneMobileNumber.Valid = true
		phoneMobileNumber.String = *data.PhoneMobileNumber
	}

	query := `
		INSERT INTO users (
			acc_id, role_id, user_state_id, name_first, name_middle, name_last, email,
			addr_home_line1, addr_home_line2, addr_home_city, addr_home_state,
			addr_home_country, addr_home_postal_code,
			addr_work_line1, addr_work_line2, addr_work_city, addr_work_state,
			addr_work_country, addr_work_postal_code,
			phone_home_cc, phone_home_number, phone_work_cc, phone_work_number,
			phone_mobile_cc, phone_mobile_number
		) VALUES (
			$1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17,$18,$19,$20,$21,$22,$23,$24,$25
		) RETURNING id
	`

	var id uint64
	var err error

	if tx != nil {
		id, err = tx.ExecGetID(
			query,
			data.AccID,
			roleID,
			userStateID,
			data.NameFirst,
			nameMiddle,
			data.NameLast,
			data.Email,
			addrHomeLine1,
			addrHomeLine2,
			addrHomeCity,
			addrHomeState,
			addrHomeCountry,
			addrHomePostalCode,
			addrWorkLine1,
			addrWorkLine2,
			addrWorkCity,
			addrWorkState,
			addrWorkCountry,
			addrWorkPostalCode,
			phoneHomeCC,
			phoneHomeNumber,
			phoneWorkCC,
			phoneWorkNumber,
			phoneMobileCC,
			phoneMobileNumber,
		)
	} else {
		err = dbutil.QueryRow(
			query,
			data.AccID,
			roleID,
			userStateID,
			data.NameFirst,
			nameMiddle,
			data.NameLast,
			data.Email,
			addrHomeLine1,
			addrHomeLine2,
			addrHomeCity,
			addrHomeState,
			addrHomeCountry,
			addrHomePostalCode,
			addrWorkLine1,
			addrWorkLine2,
			addrWorkCity,
			addrWorkState,
			addrWorkCountry,
			addrWorkPostalCode,
			phoneHomeCC,
			phoneHomeNumber,
			phoneWorkCC,
			phoneWorkNumber,
			phoneMobileCC,
			phoneMobileNumber,
		).Scan(&id)
	}

	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, errs.BadInput("relationship")
		}
		if dbutil.ErrIsUniqueViolation(err) {
			return 0, errs.BadInput("email")
		}
		return 0, err
	}

	return id, nil
}

// UserGetByID retrieves a user by its ID
func UserGetByID(id uint64) (*model.User, error) {
	query := selUser + ` WHERE id = $1 `
	row := dbutil.QueryRow(query, id)
	user, err := scanRowToUser(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("user")
		}
		return nil, err
	}

	return user, nil
}

// UserGetByEmail retrieves a user by email
func UserGetByEmail(email string) (*model.User, error) {
	query := selUser + ` WHERE email = $1 `
	row := dbutil.QueryRow(query, email)
	user, err := scanRowToUser(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errs.NotFound("user")
		}
		return nil, err
	}

	return user, nil
}

// UserUpdate updates an existing user
func UserUpdate(id uint64, data model.User) (uint64, error) {
	// Convert pointer fields to sql.Null types
	var nameMiddle sql.NullString
	if data.NameMiddle != nil {
		nameMiddle.Valid = true
		nameMiddle.String = *data.NameMiddle
	}

	var addrHomeLine1, addrHomeLine2, addrHomeCity, addrHomeState, addrHomeCountry, addrHomePostalCode sql.NullString
	if data.AddrHomeLine1 != nil {
		addrHomeLine1.Valid = true
		addrHomeLine1.String = *data.AddrHomeLine1
	}
	if data.AddrHomeLine2 != nil {
		addrHomeLine2.Valid = true
		addrHomeLine2.String = *data.AddrHomeLine2
	}
	if data.AddrHomeCity != nil {
		addrHomeCity.Valid = true
		addrHomeCity.String = *data.AddrHomeCity
	}
	if data.AddrHomeState != nil {
		addrHomeState.Valid = true
		addrHomeState.String = *data.AddrHomeState
	}
	if data.AddrHomeCountry != nil {
		addrHomeCountry.Valid = true
		addrHomeCountry.String = *data.AddrHomeCountry
	}
	if data.AddrHomePostalCode != nil {
		addrHomePostalCode.Valid = true
		addrHomePostalCode.String = *data.AddrHomePostalCode
	}

	var addrWorkLine1, addrWorkLine2, addrWorkCity, addrWorkState, addrWorkCountry, addrWorkPostalCode sql.NullString
	if data.AddrWorkLine1 != nil {
		addrWorkLine1.Valid = true
		addrWorkLine1.String = *data.AddrWorkLine1
	}
	if data.AddrWorkLine2 != nil {
		addrWorkLine2.Valid = true
		addrWorkLine2.String = *data.AddrWorkLine2
	}
	if data.AddrWorkCity != nil {
		addrWorkCity.Valid = true
		addrWorkCity.String = *data.AddrWorkCity
	}
	if data.AddrWorkState != nil {
		addrWorkState.Valid = true
		addrWorkState.String = *data.AddrWorkState
	}
	if data.AddrWorkCountry != nil {
		addrWorkCountry.Valid = true
		addrWorkCountry.String = *data.AddrWorkCountry
	}
	if data.AddrWorkPostalCode != nil {
		addrWorkPostalCode.Valid = true
		addrWorkPostalCode.String = *data.AddrWorkPostalCode
	}

	var phoneHomeCC, phoneHomeNumber, phoneWorkCC, phoneWorkNumber, phoneMobileCC, phoneMobileNumber sql.NullString
	if data.PhoneHomeCC != nil {
		phoneHomeCC.Valid = true
		phoneHomeCC.String = *data.PhoneHomeCC
	}
	if data.PhoneHomeNumber != nil {
		phoneHomeNumber.Valid = true
		phoneHomeNumber.String = *data.PhoneHomeNumber
	}
	if data.PhoneWorkCC != nil {
		phoneWorkCC.Valid = true
		phoneWorkCC.String = *data.PhoneWorkCC
	}
	if data.PhoneWorkNumber != nil {
		phoneWorkNumber.Valid = true
		phoneWorkNumber.String = *data.PhoneWorkNumber
	}
	if data.PhoneMobileCC != nil {
		phoneMobileCC.Valid = true
		phoneMobileCC.String = *data.PhoneMobileCC
	}
	if data.PhoneMobileNumber != nil {
		phoneMobileNumber.Valid = true
		phoneMobileNumber.String = *data.PhoneMobileNumber
	}

	query := `
		UPDATE users SET
			acc_id = $1, role_id = $2, user_state_id = $3, name_first = $4, name_middle = $5, name_last = $6, email = $7,
			addr_home_line1 = $8, addr_home_line2 = $9, addr_home_city = $10, addr_home_state = $11,
			addr_home_country = $12, addr_home_postal_code = $13,
			addr_work_line1 = $14, addr_work_line2 = $15, addr_work_city = $16, addr_work_state = $17,
			addr_work_country = $18, addr_work_postal_code = $19,
			phone_home_cc = $20, phone_home_number = $21, phone_work_cc = $22, phone_work_number = $23,
			phone_mobile_cc = $24, phone_mobile_number = $25,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = $26
	`

	rowsAffected, err := dbutil.ExecGetAffRows(
		query,
		data.AccID,
		data.RoleID,
		data.UserStateID,
		data.NameFirst,
		nameMiddle,
		data.NameLast,
		data.Email,
		addrHomeLine1,
		addrHomeLine2,
		addrHomeCity,
		addrHomeState,
		addrHomeCountry,
		addrHomePostalCode,
		addrWorkLine1,
		addrWorkLine2,
		addrWorkCity,
		addrWorkState,
		addrWorkCountry,
		addrWorkPostalCode,
		phoneHomeCC,
		phoneHomeNumber,
		phoneWorkCC,
		phoneWorkNumber,
		phoneMobileCC,
		phoneMobileNumber,
		id,
	)

	if err != nil {
		if dbutil.ErrIsUniqueViolation(err) {
			return 0, errs.BadInput("email")
		}
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, errs.BadInput("relationship")
		}
		return 0, err
	}

	return rowsAffected, nil
}

// UserDelete deletes a user by ID
func UserDelete(id uint64) (uint64, error) {
	query := `DELETE FROM users WHERE id = $1`

	rowsAffected, err := dbutil.ExecGetAffRows(query, id)
	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return 0, errs.BadInput("user-in-use")
		}
		return 0, err
	}

	return rowsAffected, nil
}

// UserList retrieves a list of users with pagination
func UserList(page apitypes.Page) ([]*model.User, error) {
	query := selUser + ` ORDER BY created_at DESC ` + page.AsSQLLimit()
	rows, err := dbutil.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var users []*model.User
	for rows.Next() {
		user, err := scanRowToUser(rows)
		if err != nil {
			return nil, err
		}
		users = append(users, user)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return users, nil
}
