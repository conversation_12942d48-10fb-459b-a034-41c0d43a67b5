package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/apitypes"
)

func (c *Client) PermissionCreate(ctx context.Context, data apitypes.IDNameType) (uint64, error) {
	url := c.UrlPfx + accmgtPermission
	return c.Client.Client.Create(ctx, url, data, nil)
}

func (c *Client) PermissionGetByID(ctx context.Context, id uint64) (*apitypes.IDNameType, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtPermission, id)
	var result apitypes.IDNameType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) PermissionGetByName(ctx context.Context, name string) (*apitypes.IDNameType, error) {
	url := fmt.Sprintf("%s%s/name/%s", c.UrlPfx, accmgtPermission, name)
	var result apitypes.IDNameType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) PermissionUpdate(ctx context.Context, id uint64, permission apitypes.IDNameType) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtPermission, id)
	return c.Client.Client.Update(ctx, url, permission, nil)
}

func (c *Client) PermissionDelete(ctx context.Context, id uint64) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtPermission, id)
	return c.Client.Client.Delete(ctx, url, nil)
}

func (c *Client) PermissionList(ctx context.Context, page apitypes.Page) ([]apitypes.IDNameType, error) {
	url := c.UrlPfx + accmgtPermissions + "?" + page.AsURLParams()
	var result []apitypes.IDNameType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
