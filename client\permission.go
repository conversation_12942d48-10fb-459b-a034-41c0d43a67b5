package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/clientcommon"
)

func (c *Client) PermissionCreate(ctx context.Context, permission apitypes.IDNameType) (*apitypes.IDType, error) {
	var result apitypes.IDType
	err := clientcommon.DoRequest(ctx, "POST", accmgtPermission, permission, &result)
	return &result, err
}

func (c *Client) PermissionGetByID(ctx context.Context, id uint64) (*apitypes.IDNameType, error) {
	url := fmt.Sprintf("%s/%d", accmgtPermission, id)
	var result apitypes.IDNameType
	err := clientcommon.DoRequest(ctx, "GET", url, nil, &result)
	return &result, err
}

func (c *Client) PermissionGetByName(ctx context.Context, name string) (*apitypes.IDNameType, error) {
	url := fmt.Sprintf("%s/name/%s", accmgtPermission, name)
	var result apitypes.IDNameType
	err := clientcommon.DoRequest(ctx, "GET", url, nil, &result)
	return &result, err
}

func (c *Client) PermissionUpdate(ctx context.Context, id uint64, permission apitypes.IDNameType) (*apitypes.AffectedRowsType, error) {
	url := fmt.Sprintf("%s/%d", accmgtPermission, id)
	var result apitypes.AffectedRowsType
	err := clientcommon.DoRequest(ctx, "PUT", url, permission, &result)
	return &result, err
}

func (c *Client) PermissionDelete(ctx context.Context, id uint64) (*apitypes.AffectedRowsType, error) {
	url := fmt.Sprintf("%s/%d", accmgtPermission, id)
	var result apitypes.AffectedRowsType
	err := clientcommon.DoRequest(ctx, "DELETE", url, nil, &result)
	return &result, err
}

func (c *Client) PermissionList(ctx context.Context, page apitypes.Page) ([]apitypes.IDNameType, error) {
	url := fmt.Sprintf("%s?%s", accmgtPermissions, page.AsURLParams())
	var result []apitypes.IDNameType
	err := clientcommon.DoRequest(ctx, "GET", url, nil, &result)
	return result, err
}
