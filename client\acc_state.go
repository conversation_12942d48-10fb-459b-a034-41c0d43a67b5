package client

import (
	"context"
	"fmt"

	"github.com/homewizeAI/apitypes"
)

func (c *Client) AccStateCreate(ctx context.Context, data apitypes.IDNameType) (uint64, error) {
	url := c.UrlPfx + accmgtAccState
	return c.Client.Client.Create(ctx, url, data, nil)
}

func (c *Client) AccStateGetByID(ctx context.Context, id uint64) (*apitypes.IDNameType, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtAccState, id)
	var result apitypes.IDNameType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) AccStateGetByName(ctx context.Context, name string) (*apitypes.IDNameType, error) {
	url := fmt.Sprintf("%s%s/name/%s", c.UrlPfx, accmgtAccState, name)
	var result apitypes.IDNameType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (c *Client) AccStateUpdate(ctx context.Context, id uint64, data apitypes.IDNameType) (uint64, error) {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtAccState, id)
	return c.Client.Client.Update(ctx, url, data, nil)
}

func (c *Client) AccStateDelete(ctx context.Context, id uint64) error {
	url := fmt.Sprintf("%s%s/%d", c.UrlPfx, accmgtAccState, id)
	_, err := c.Client.Client.Delete(ctx, url, nil)
	return err
}

func (c *Client) AccStateList(ctx context.Context, page apitypes.Page) ([]apitypes.IDNameType, error) {
	url := c.UrlPfx + accmgtAccStates + "?" + page.AsURLParams()
	var result []apitypes.IDNameType
	err := c.Client.Client.Get(ctx, url, nil, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
