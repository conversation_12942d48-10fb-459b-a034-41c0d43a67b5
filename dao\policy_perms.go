package dao

import (
	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/dbutil"
	"github.com/homewizeAI/errs"
)

const (
	selPolicyPerm = `SELECT policy_id, permission_id, created_at FROM policy_perms `
)

// scanRowToPolicyPerm scans a database row into a PolicyPerm struct
func scanRowToPolicyPerm(row interface{ Scan(...interface{}) error }) (*model.PolicyPerm, error) {
	var policyPerm model.PolicyPerm
	err := row.Scan(&policyPerm.PolicyID, &policyPerm.PermissionID, &policyPerm.CreatedAt)
	return &policyPerm, err
}

func PolicyPermCreate(data model.PolicyPermRequest) error {
	query := `
		INSERT INTO policy_perms (
			policy_id, permission_id
		) VALUES (
			$1, $2
		)
	`

	_, err := dbutil.ExecGetAffRows(query, data.PolicyID, data.PermissionID)
	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return errs.BadInput("relationship")
		}
		if dbutil.ErrIsUniqueViolation(err) {
			return errs.BadInput("duplicate")
		}
		return err
	}

	return nil
}

func PolicyPermGetByPolicyID(policyID uint64) ([]model.PolicyPerm, error) {
	query := selPolicyPerm + `WHERE policy_id = $1 ORDER BY permission_id`
	rows, err := dbutil.Query(query, policyID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var policyPerms []model.PolicyPerm
	for rows.Next() {
		policyPerm, err := scanRowToPolicyPerm(rows)
		if err != nil {
			return nil, err
		}
		policyPerms = append(policyPerms, *policyPerm)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return policyPerms, nil
}

func PolicyPermGetByPermissionID(permissionID uint64) ([]model.PolicyPerm, error) {
	query := selPolicyPerm + `WHERE permission_id = $1 ORDER BY policy_id`
	rows, err := dbutil.Query(query, permissionID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var policyPerms []model.PolicyPerm
	for rows.Next() {
		policyPerm, err := scanRowToPolicyPerm(rows)
		if err != nil {
			return nil, err
		}
		policyPerms = append(policyPerms, *policyPerm)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return policyPerms, nil
}

func PolicyPermDelete(policyID, permissionID uint64) (uint64, error) {
	query := `DELETE FROM policy_perms WHERE policy_id = $1 AND permission_id = $2`
	affectedRows, err := dbutil.ExecGetAffRows(query, policyID, permissionID)
	if err != nil {
		return 0, err
	}
	return affectedRows, nil
}

func PolicyPermList(page apitypes.Page) ([]model.PolicyPerm, error) {
	query := selPolicyPerm + `ORDER BY policy_id, permission_id ` + page.AsSQLLimit()
	rows, err := dbutil.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var policyPerms []model.PolicyPerm
	for rows.Next() {
		policyPerm, err := scanRowToPolicyPerm(rows)
		if err != nil {
			return nil, err
		}
		policyPerms = append(policyPerms, *policyPerm)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return policyPerms, nil
}
