CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    acc_id INTEGER NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    role_id INTEGER NOT NULL REFERENCES roles(id) ON DELETE RESTRICT,
    user_state_id INTEGER NOT NULL REFERENCES user_states(id) ON DELETE RESTRICT,
    name_first VARCHAR(256) NOT NULL,
    name_middle VARCHAR(256),
    name_last VARCHAR(256) NOT NULL,
    email VARCHAR(512) NOT NULL UNIQUE,
    addr_home_line1 VARCHAR(512),
    addr_home_line2 VARCHAR(1024),
    addr_home_city VARCHAR(256),
    addr_home_state VARCHAR(256),
    addr_home_country VARCHAR(256),
    addr_home_postal_code VARCHAR(256),
    addr_work_line1 VARCHAR(512),
    addr_work_line2 VARCHAR(1024),
    addr_work_city VARCHAR(256),
    addr_work_state VARCHAR(256),
    addr_work_country VARCHAR(256),
    addr_work_postal_code VA<PERSON>HAR(256),
    phone_home_cc VARCHAR(256),
    phone_home_number VA<PERSON>HAR(256),
    phone_work_cc VARCHAR(256),
    phone_work_number VARCHAR(256),
    phone_mobile_cc VARCHAR(256),
    phone_mobile_number VARCHAR(256),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
