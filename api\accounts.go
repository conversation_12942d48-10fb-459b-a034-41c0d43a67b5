package api

import (
	"github.com/gin-gonic/gin"

	"github.com/homewizeAI/accmgtms/dao"
	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/errs"
	"github.com/homewizeAI/utils"
)

func AccCreate(c *gin.Context) {
	var err error
	var data model.Account
	if err = c.BindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	id, err := dao.AccCreate(data)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		if errs.IsConflict(err) {
			apicommon.RespConflict(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}
	apicommon.RespOk(c, apitypes.IDTypeNew(id))
}

func AccGetByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	account, err := dao.AccGetByID(id)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, account)
}

func AccGetByShortName(c *gin.Context) {
	shortName := c.Param("shortName")
	if shortName == "" {
		apicommon.RespBadRequest(c, "short-name")
		return
	}

	account, err := dao.AccGetByShortName(shortName)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, account)
}

func AccUpdate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	var data model.Account
	if err = c.BindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	rowsAffected, err := dao.AccUpdate(id, data)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(rowsAffected))
}

func AccDelete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	rowsAffected, err := dao.AccDelete(id)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(rowsAffected))
}

func AccList(c *gin.Context) {
	page := apicommon.GetPage(c)
	accounts, err := dao.AccList(page)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, accounts)
}

func AccCount(c *gin.Context) {
	count, err := dao.AccCount()
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, apitypes.CountTypeNew(count))
}

func AccShortNameIsAvailable(c *gin.Context) {
	shortName := c.Param("shortName")
	if shortName == "" {
		apicommon.RespBadRequest(c, "short-name")
		return
	}

	// Validate short name format (basic validation)
	if len(shortName) < 2 || len(shortName) > 50 {
		apicommon.RespBadRequest(c, "short-name-length")
		return
	}

	available, err := dao.AccCheckShortNameAvailable(shortName)
	if err != nil {
		apicommon.RespISE(c, err)
		return
	}

	apicommon.RespOk(c, apitypes.BoolTypeNew(available))
}
