package dao

import (
	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/dbutil"
)

// Signup creates an account, user, and user activation token in a transaction
// Returns account ID, user ID, activation key ID, and activation token
func Signup(request model.SignupRequest) (*model.SignupResponse, error) {
	var response model.SignupResponse

	// Begin transaction
	tx, err := dbutil.BeginTransaction()
	if err != nil {
		return nil, err
	}
	defer tx.Rollback() // This will be a no-op if tx.Commit() is called

	// Create account
	accID, err := accCreate(tx, request.Account)
	if err != nil {
		return nil, err
	}
	response.AccID = accID

	// Set the account ID and default user state for the user
	request.User.AccID = accID
	// Set default user state to "Not Validated" for new signups
	if request.User.UserStateID == 0 {
		request.User.UserStateID = model.UserStateNotValidated
	}

	// Create user
	userID, err := userCreate(tx, request.User)
	if err != nil {
		return nil, err
	}
	response.UserID = userID

	// Create user activation token
	activationKeyID, activationToken, err := getOrCreateUserActivationTokenTx(tx, userID)
	if err != nil {
		return nil, err
	}
	response.ActivationKeyID = activationKeyID
	response.ActivationToken = activationToken

	// Commit transaction
	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &response, nil
}
