#!/bin/bash

# Simple deployment script for Account Management Service

set -e

echo "🚀 Deploying Account Management Service..."

# Create configuration if it doesn't exist
if [ ! -f "conf/secrets.toml" ]; then
    echo "📝 Creating configuration file..."
    mkdir -p conf
    cat > conf/secrets.toml << EOF
[database]
driver = "postgres"
dataSource = "postgresql://accmgtms_user:accmgtms_pass@localhost:5432/accmgtms?sslmode=disable"
EOF
    echo "⚠️  Please update conf/secrets.toml with your database settings"
fi

# Build the application
echo "🔨 Building application..."
./build.sh

# Start the application
echo "� Starting Account Management Service..."
echo "📋 Service will be available at: http://localhost:3000"
echo "🔧 Press Ctrl+C to stop"
echo ""

./accmgtms
