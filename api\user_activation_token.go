package api

import (
	"github.com/gin-gonic/gin"

	"github.com/homewizeAI/accmgtms/dao"
	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/errs"
	"github.com/homewizeAI/utils"
)

func UserActivationTokenCreate(c *gin.Context) {
	userIDStr := c.Param("userID")
	userID, err := utils.AsUint64(userIDStr)
	if err != nil {
		apicommon.RespBadRequest(c, "user-id")
		return
	}

	_, _, err = dao.UserActivationTokenCreate(userID)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		if errs.IsConflict(err) {
			apicommon.RespConflict(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, err.Error())
		return
	}

	// Get the full user activation token struct by calling get by user ID
	userActivationToken, err := dao.UserActivationTokenGetByUserID(userID)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, userActivationToken)
}

func UserActivationTokenGetByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	token, err := dao.UserActivationTokenGetByID(id)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, token)
}

func UserActivationTokenGetByToken(c *gin.Context) {
	token := c.Param("token")
	if token == "" {
		apicommon.RespBadRequest(c, "token")
		return
	}

	tokenData, err := dao.UserActivationTokenGetByToken(token)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, tokenData)
}

func UserActivationTokenGetByUserID(c *gin.Context) {
	userIDStr := c.Param("userID")
	userID, err := utils.AsUint64(userIDStr)
	if err != nil {
		apicommon.RespBadRequest(c, "user-id")
		return
	}

	token, err := dao.UserActivationTokenGetByUserID(userID)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, token)
}

func UserActivationTokenUpdate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	var data model.UserActivationToken
	if err = c.BindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	rowsAffected, err := dao.UserActivationTokenUpdate(id, data)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(rowsAffected))
}

func UserActivationTokenDelete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := utils.AsUint64(idStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	rowsAffected, err := dao.UserActivationTokenDelete(id)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "internal-error")
		return
	}

	apicommon.RespOk(c, apitypes.AffectedRowsTypeNew(rowsAffected))
}
