package dao

import (
	"database/sql"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
	"github.com/homewizeAI/dbutil"
	"github.com/homewizeAI/errs"
)

const (
	selRolePolicy = `SELECT role_id, policy_id, created_at FROM role_policies `
)

// scanRowToRolePolicy scans a database row into a RolePolicy struct
func scanRowToRolePolicy(row interface{ Scan(...interface{}) error }) (*model.RolePolicy, error) {
	var rolePolicy model.RolePolicy
	err := row.Scan(&rolePolicy.RoleID, &rolePolicy.PolicyID, &rolePolicy.CreatedAt)
	return &rolePolicy, err
}

func RolePolicyCreate(data model.RolePolicyRequest) error {
	query := `
		INSERT INTO role_policies (
			role_id, policy_id
		) VALUES (
			$1, $2
		)
	`

	_, err := dbutil.Exec(query, data.RoleID, data.PolicyID)
	if err != nil {
		if dbutil.ErrIsFKChildViolation(err) {
			return errs.BadInput("relationship")
		}
		if dbutil.ErrIsUniqueViolation(err) {
			return errs.BadInput("duplicate")
		}
		return err
	}

	return nil
}

func RolePolicyGetByRoleID(roleID uint64) ([]model.RolePolicy, error) {
	query := selRolePolicy + `WHERE role_id = $1 ORDER BY policy_id`
	rows, err := dbutil.Query(query, roleID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var rolePolicies []model.RolePolicy
	for rows.Next() {
		rolePolicy, err := scanRowToRolePolicy(rows)
		if err != nil {
			return nil, err
		}
		rolePolicies = append(rolePolicies, *rolePolicy)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return rolePolicies, nil
}

func RolePolicyGetByPolicyID(policyID uint64) ([]model.RolePolicy, error) {
	query := selRolePolicy + `WHERE policy_id = $1 ORDER BY role_id`
	rows, err := dbutil.Query(query, policyID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var rolePolicies []model.RolePolicy
	for rows.Next() {
		rolePolicy, err := scanRowToRolePolicy(rows)
		if err != nil {
			return nil, err
		}
		rolePolicies = append(rolePolicies, *rolePolicy)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return rolePolicies, nil
}

func RolePolicyDelete(roleID, policyID uint64) (uint64, error) {
	query := `DELETE FROM role_policies WHERE role_id = $1 AND policy_id = $2`
	affectedRows, err := dbutil.ExecGetAffRows(query, roleID, policyID)
	if err != nil {
		return 0, err
	}
	return affectedRows, nil
}

func RolePolicyList(page apitypes.Page) ([]model.RolePolicy, error) {
	query := selRolePolicy + `ORDER BY role_id, policy_id ` + page.AsSQLLimit()
	rows, err := dbutil.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var rolePolicies []model.RolePolicy
	for rows.Next() {
		rolePolicy, err := scanRowToRolePolicy(rows)
		if err != nil {
			return nil, err
		}
		rolePolicies = append(rolePolicies, *rolePolicy)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return rolePolicies, nil
}
