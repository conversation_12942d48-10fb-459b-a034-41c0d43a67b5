package client

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apitypes"
	"github.com/jarcoal/httpmock"
)

func TestUserCreate(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"POST",
		"http://localhost:3000/accmgt/user",
		func(req *http.Request) (*http.Response, error) {
			var user model.User
			err := json.NewDecoder(req.Body).Decode(&user)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			var resp apitypes.IDType
			resp.ID = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	var user model.User
	user.AccID = 1
	user.RoleID = 1
	user.UserStateID = 1
	user.NameFirst = "John"
	user.NameLast = "Doe"
	user.Email = "<EMAIL>"
	id, err := c.UserCreate(context.Background(), user)
	if err != nil {
		t.Error(err)
	}
	if id != 1 {
		t.Errorf("Expected id 1, got %d", id)
	}
}

func TestUserGetByID(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/user/1",
		func(req *http.Request) (*http.Response, error) {
			user := model.User{
				ID:        1,
				AccID:     1,
				RoleID:    1,
				NameFirst: "John",
				NameLast:  "Doe",
				Email:     "<EMAIL>",
			}
			return httpmock.NewJsonResponse(http.StatusOK, user)
		},
	)

	user, err := c.UserGetByID(context.Background(), 1)
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if user == nil {
		t.Error("Expected user response, got nil")
	}
}

func TestUserGetByEmail(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/user/email/<EMAIL>",
		func(req *http.Request) (*http.Response, error) {
			user := model.User{
				ID:        1,
				AccID:     1,
				RoleID:    1,
				NameFirst: "John",
				NameLast:  "Doe",
				Email:     "<EMAIL>",
			}
			return httpmock.NewJsonResponse(http.StatusOK, user)
		},
	)

	user, err := c.UserGetByEmail(context.Background(), "<EMAIL>")
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if user == nil {
		t.Error("Expected user response, got nil")
	}
}

func TestUserUpdate(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"PUT",
		"http://localhost:3000/accmgt/user/1",
		func(req *http.Request) (*http.Response, error) {
			var user model.User
			err := json.NewDecoder(req.Body).Decode(&user)
			if err != nil {
				t.Error(err)
				out := map[string]string{"error": "bad-request"}
				return httpmock.NewJsonResponse(http.StatusInternalServerError, out)
			}
			var resp apitypes.AffectedRowsType
			resp.AffectedRows = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	var user model.User
	user.AccID = 1
	user.RoleID = 1
	user.UserStateID = 1
	user.NameFirst = "Jane"
	user.NameLast = "Doe"
	user.Email = "<EMAIL>"
	rowsAffected, err := c.UserUpdate(context.Background(), 1, user)
	if err != nil {
		t.Error(err)
	}
	if rowsAffected != 1 {
		t.Errorf("Expected 1 row affected, got %d", rowsAffected)
	}
}

func TestUserDelete(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"DELETE",
		"http://localhost:3000/accmgt/user/1",
		func(req *http.Request) (*http.Response, error) {
			var resp apitypes.AffectedRowsType
			resp.AffectedRows = 1
			return httpmock.NewJsonResponse(http.StatusOK, resp)
		},
	)

	rowsAffected, err := c.UserDelete(context.Background(), 1)
	if err != nil {
		t.Error(err)
	}
	if rowsAffected != 1 {
		t.Errorf("Expected 1 row affected, got %d", rowsAffected)
	}
}

func TestUserList(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder(
		"GET",
		"http://localhost:3000/accmgt/users",
		func(req *http.Request) (*http.Response, error) {
			users := []model.User{
				{ID: 1, AccID: 1, RoleID: 1, NameFirst: "John", NameLast: "Doe", Email: "<EMAIL>"},
				{ID: 2, AccID: 1, RoleID: 2, NameFirst: "Jane", NameLast: "Smith", Email: "<EMAIL>"},
			}
			return httpmock.NewJsonResponse(http.StatusOK, users)
		},
	)

	page := apitypes.Page{Page: 0, Size: 20}
	users, err := c.UserList(context.Background(), page)
	if err != nil {
		t.Error(err)
	}
	// Simple connectivity test - just verify we got a response
	if users == nil {
		t.Error("Expected users response, got nil")
	}
}
