apiVersion: batch/v1
kind: Job
metadata:
  name: accmgtms-migrate
  namespace: default
  labels:
    app: accmgtms-migrate
spec:
  template:
    metadata:
      labels:
        app: accmgtms-migrate
    spec:
      restartPolicy: OnFailure
      containers:
      - name: accmgtms-migrate
        image: github.com/homewizeAI/accmgtms-migrate:latest
        volumeMounts:
        - name: config-volume
          mountPath: /app/conf
          readOnly: true
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
      volumes:
      - name: config-volume
        configMap:
          name: accmgtms-config
  backoffLimit: 3
