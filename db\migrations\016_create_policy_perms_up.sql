-- Create policy_perms link table
CREATE TABLE policy_perms (
    policy_id INTEGER NOT NULL,
    permission_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_policy_perms_policy_id
        FOREIGN KEY (policy_id) REFERENCES policies(id) ON DELETE CASCADE,
    CONSTRAINT fk_policy_perms_permission_id
        FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    
    -- Composite primary key to prevent duplicates
    PRIMARY KEY (policy_id, permission_id)
);

-- Create indexes for better performance
CREATE INDEX idx_policy_perms_policy_id ON policy_perms(policy_id);
CREATE INDEX idx_policy_perms_permission_id ON policy_perms(permission_id);
