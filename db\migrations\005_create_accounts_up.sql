CREATE TABLE accounts (
    id SERIAL PRIMARY KEY,
    acc_state_id INTEGER REFERENCES acc_states(id) ON DELETE RESTRICT,
    acc_state_reason_id INTEGER REFERENCES acc_state_reason(id) ON DELETE RESTRICT,
    name_registered VARCHAR(1024) NOT NULL,
    name_short VARCHAR(256) NOT NULL UNIQUE,
    addr_line1 VARCHAR(512),
    addr_line2 VARCHAR(1024),
    addr_city VARCHAR(256),
    addr_state VARCHAR(256),
    addr_country VARCHAR(256),
    addr_postal_code VARCHAR(256),
    phone_cc VARCHAR(256), -- country code
    phone_number VA<PERSON>HAR(256) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
